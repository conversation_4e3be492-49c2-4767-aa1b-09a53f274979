# task list:  object state → attr. action → attr. size →  attr. material →attr. color 
vqa_path: 'datasets/gqa-checklist-new'
task_list:
  - name: gqa_relation
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_relation.json' , 'datasets/gqa-checklist-new/val_relation.json' ,'datasets/gqa-checklist-new/test_relation.json' ]
  - name: gqa_action
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_action.json' , 'datasets/gqa-checklist-new/val_action.json' ,'datasets/gqa-checklist-new/test_action.json' ]
  - name: gqa_object
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_object.json' , 'datasets/gqa-checklist-new/val_object.json' ,'datasets/gqa-checklist-new/test_object.json' ]
  - name: gqa_position
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_position.json' , 'datasets/gqa-checklist-new/val_position.json' ,'datasets/gqa-checklist-new/test_position.json' ]
  - name: gqa_color
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_color.json' , 'datasets/gqa-checklist-new/val_color.json' ,'datasets/gqa-checklist-new/test_color.json' ]
  - name: gqa_material
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_material.json' , 'datasets/gqa-checklist-new/val_material.json' ,'datasets/gqa-checklist-new/test_material.json' ]
  - name: gqa_logical
    config: 'configs/task/gqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/gqa-checklist-new/train_logical.json' , 'datasets/gqa-checklist-new/val_logical.json' ,'datasets/gqa-checklist-new/test_logical.json' ]

# task order
order: fixed # fixed or random
# set pretrained as a file path or an url
#pretrained: '/data/home/<USER>/checkpoints/model_base.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_base_capfilt_large.pth'
pretrained: '/data/home/<USER>/checkpoints/model_vqa.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_base_vqa_capfilt_large.pth'



