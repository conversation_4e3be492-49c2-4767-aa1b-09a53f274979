# task list:  object state → attr. action → attr. size →  attr. material →attr. color 
vqa_path: 'datasets/path-vqa'
task_list:
  - name: pathvqa_What
    config: 'configs/task/pathvqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/path-vqa/What_train.json' , 'datasets/path-vqa/What_val.json' ,'datasets/path-vqa/What_test.json' ]
  - name: pathvqa_Judge
    config: 'configs/task/pathvqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/path-vqa/Judge_train.json' , 'datasets/path-vqa/Judge_val.json' ,'datasets/path-vqa/Judge_test.json' ]
# task order
order: fixed # fixed or random
# set pretrained as a file path or an url
#pretrained: '/root/xingxing/ckpt/model_base.pth'
#pretrained: '/root/xingxing/ckpt/model_base_capfilt_large.pth'
pretrained: '/root/xingxing/ckpt/model_vqa.pth'
#pretrained: '/root/xingxing/ckpt/model_base_vqa_capfilt_large.pth'



