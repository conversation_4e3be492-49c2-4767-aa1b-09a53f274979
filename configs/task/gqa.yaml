vg_root: '/root/xingxing/data/gqa/' #followed by train2014/
dataset: 'vg'

# size of vit model; base or large
vit: 'base'
#batch_size_train: [16,8,8,8,8,8,8,8]
#batch_size_train: [16,16,16,16,16,16,16,16,16,16]
#batch_size_test: 64
#batch_size_train: [32,32,32,32,32,32,32,32,32,32]
#batch_size_train: [32,24,24,24,24,24,24,24,24,24]
#batch_size_train: [4,1,1,1,1,1,1,1,1,1]
batch_size_train: [60,24,24,24,20,20,20,20,20,20]
#batch_size_train: [48,16,16,16,16,16,16,16,16,16]
#batch_size_train: [48,48,48,48,48,48,48,48,48,48]
#batch_size_train: [62,62,62,62,62,62,62,62,62,62]
#batch_size_train: [56,56,56,56,56,56,56,56,56,56]
batch_size_test: 128
vit_grad_ckpt: False
vit_ckpt_layer: 0
max_epoch: 12

image_size: 480

#k_test: 128
#inference: 'rank'
inference: 'generate'

# optimizer
weight_decay: 0.05
#init_lr: 3e-5
init_lr: 0.00125
min_lr: 0
