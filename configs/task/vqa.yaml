vqa_root: "/data1/home/<USER>/EQA/qa/datasets/coco" #followed by train2014/
dataset: "vqa"

# size of vit model; base or large
vit: "base"
#batch_size_train: [16,8,8,8,8,8,8,8]
#batch_size_train: [16,16,16,16,16,16,16,16]
#batch_size_train: [22,22,22,22,22,22,22,22]
#batch_size_train: [32,32,32,32,32,32,32,32,32,32]
#batch_size_train: [32,32,32,32,32,32,32,32]
#batch_size_train: [48,48,48,48,48,48,48,48,48,48]
#batch_size_train: [48,28,28,28,28,28,28,28,28,28,28]
#batch_size_train: [48,4,4,4,4,4,4,4,4,4,4]
#batch_size_train: [48,16,16,16,16,16,16,16,16,16]
#batch_size_train: [56,16,16,16,16,16,16,16,16,16]
#batch_size_train: [60,24,24,24,24,24,24,24,24,24]
batch_size_train: [32, 32, 32, 32, 32, 32, 32, 32, 32, 32]
#batch_size_train: [48,1,1,1,1,1,1,1,1,1,1]
#batch_size_train: [62,62,62,62,62,62,62,62,62,62]
batch_size_test: 32
vit_grad_ckpt: False
vit_ckpt_layer: 0
max_epoch: 1

image_size: 480

#k_test: 128
#inference: 'rank'
inference: "generate"

# optimizer
weight_decay: 0.05
#init_lr: 3e-5
init_lr: 0.00125
min_lr: 0
