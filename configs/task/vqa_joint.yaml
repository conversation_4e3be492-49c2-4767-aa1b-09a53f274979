vqa_root: '/data/home/<USER>/data/COCO/' #followed by train2014/
dataset: 'vqa'

# size of vit model; base or large
vit: 'base'
#batch_size_train: [16,8,8,8,8,8,8,8]
#batch_size_train: [16,16,16,16,16,16,16,16]
#batch_size_train: [32,32,32,32,32,32,32,32]
#batch_size_train: [48,48,48,48,48,48,48,48,48,48]
batch_size_train: [96,62,62,62,62,62,62,62,62,62]
#batch_size_train: [56,56,56,56,56,56,56,56,56,56]
batch_size_test: 96
vit_grad_ckpt: False
vit_ckpt_layer: 0
max_epoch: 20

image_size: 480

#k_test: 128
#inference: 'rank'
inference: 'generate'

# optimizer
weight_decay: 0.05
#init_lr: 3e-5
init_lr: 0.00125
min_lr: 0
