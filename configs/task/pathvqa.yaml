
pathvqa_root: '/root/xingxing/data/pvqa/images/' #followed by train2014/
dataset: 'pathvqa'

# size of vit model; base or large
vit: 'base'
#batch_size_train: [16,8,8,8,8,8,8,8]
#batch_size_train: [16,16,16,16,16,16,16,16,16,16]
#batch_size_test: 64
#batch_size_train: [32,32,32,32,32,32,32,32,32,32]
#batch_size_train: [32,24,24,24,24,24,24,24,24,24]
#batch_size_train: [4,1,1,1,1,1,1,1,1,1]
#batch_size_train: [16,24,24,24,20,20,20,20,20,20]
#batch_size_train: [48,16,16,16,16,16,16,16,16,16]
#batch_size_train: [48,48,48,48,48,48,48,48,48,48]
#batch_size_train: [64,64,64,64,64,64,64,64,64,64]
batch_size_train: [96,32,32]
#batch_size_train: [56,56,56,56,56,56,56,56,56,56]
batch_size_test: 128
vit_grad_ckpt: False
vit_ckpt_layer: 0
max_epoch: 30

image_size: 384

#k_test: 128
#inference: 'rank'
inference: 'generate'

# optimizer
weight_decay: 0.05
init_lr: 0.00002
#init_lr: 0.00125
min_lr: 0
