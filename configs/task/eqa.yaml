image_root: "datasets/eqa"
answer_list: "configs/answer_list.json"

# EQA train files
train_file:
  [
    "datasets/eqa-checklist/train_q_color.json",
    "datasets/eqa-checklist/train_q_location.json",
    "datasets/eqa-checklist/train_q_material.json",
    "datasets/eqa-checklist/train_q_purpose.json",
    "datasets/eqa-checklist/train_q_relationship.json",
    "datasets/eqa-checklist/train_q_state.json",
  ]

# EQA val files
val_file:
  [
    "datasets/eqa-checklist/val_q_color.json",
    "datasets/eqa-checklist/val_q_location.json",
    "datasets/eqa-checklist/val_q_material.json",
    "datasets/eqa-checklist/val_q_purpose.json",
    "datasets/eqa-checklist/val_q_relationship.json",
    "datasets/eqa-checklist/val_q_state.json",
  ]

# EQA test files
test_file:
  [
    "datasets/eqa-checklist/test_q_color.json",
    "datasets/eqa-checklist/test_q_location.json",
    "datasets/eqa-checklist/test_q_material.json",
    "datasets/eqa-checklist/test_q_purpose.json",
    "datasets/eqa-checklist/test_q_relationship.json",
    "datasets/eqa-checklist/test_q_state.json",
  ]

# size of vit model; base or large
vit: "base"
batch_size_train: [32, 32, 32, 32, 32, 32, 32, 32, 32, 32]
batch_size_test: 32
vit_grad_ckpt: False
vit_ckpt_layer: 0
max_epoch: 2

image_size: 480

inference: "generate"

# optimizer
weight_decay: 0.05
init_lr: 0.00125
min_lr: 0
