# task list: object state → attr. action → attr. size → rel. spatial → attr. material → rel. action → attr. color
vl_checklist_path: 'datasets/vl-checklist/data'
task_list:
  - name: vl_checklist_state
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: [ 'datasets/vl-checklist/data/Attribute/**/state.json' ]
  - name: vl_checklist_action
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: [ 'datasets/vl-checklist/data/Attribute/**/action.json' ]
  - name: vl_checklist_size
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: [ 'datasets/vl-checklist/data/Attribute/**/size.json' ]
  - name: vl_checklist_spatial
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: [ 'datasets/vl-checklist/data/Relation/**/spatial.json' ]
  - name: vl_checklist_material
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: [ 'datasets/vl-checklist/data/Attribute/**/material.json' ]
  - name: vl_checklist_relation_action
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: [ 'datasets/vl-checklist/data/Relation/**/action.json' ]
  - name: vl_checklist_color
    config: 'configs/task/nlvr_vl_checklist.yaml'
    trainer: 'train_nlvr'
    json_files: ['datasets/vl-checklist/data/Attribute/**/*color.json']

# task order
order: fixed # fixed or random

# set pretrained as a file path or an url
#pretrained: '/data/multi-modal/ckpt/model_base_capfilt_large.pth'
#pretrained: '/data/multi-modal/ckpt/model_base.pth'
pretrained: '/data/multi-modal/ckpt/model_base_nlvr.pth'
