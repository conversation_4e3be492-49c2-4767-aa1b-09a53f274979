# task list:  object state → attr. action → attr. size →  attr. material →attr. color 
vqa_path: 'datasets/vqa-cp-checklist'
task_list:
  - name: vqa_material
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_material.json' , 'datasets/vqa-cp-checklist/val_q_material.json' ,'datasets/vqa-cp-checklist/test_q_material.json' ]
  - name: vqa_location
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_location.json' , 'datasets/vqa-cp-checklist/val_q_location.json' ,'datasets/vqa-cp-checklist/test_q_location.json' ]
  - name: vqa_commonsense
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_commonsense.json' , 'datasets/vqa-cp-checklist/val_q_commonsense.json' ,'datasets/vqa-cp-checklist/test_q_commonsense.json' ]
  - name: vqa_action
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_action.json' , 'datasets/vqa-cp-checklist/val_q_action.json' ,'datasets/vqa-cp-checklist/test_q_action.json' ]
  - name: vqa_subcategory
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_subcategory.json' , 'datasets/vqa-cp-checklist/val_q_subcategory.json' ,'datasets/vqa-cp-checklist/test_q_subcategory.json' ]
  - name: vqa_color
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_color.json' , 'datasets/vqa-cp-checklist/val_q_color.json' ,'datasets/vqa-cp-checklist/test_q_color.json' ]
  - name: vqa_count
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_count.json' , 'datasets/vqa-cp-checklist/val_q_count.json' ,'datasets/vqa-cp-checklist/test_q_count.json' ]
  - name: vqa_judge
    config: 'configs/task/vqa.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-cp-checklist/train_q_judge.json' , 'datasets/vqa-cp-checklist/val_q_judge.json' ,'datasets/vqa-cp-checklist/test_q_judge.json' ]

# task order
order: fixed # fixed or random

# set pretrained as a file path or an url
pretrained: '/data/home/<USER>/checkpoints/model_base.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_base_capfilt_large.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_vqa.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_base_vqa_capfilt_large.pth'



