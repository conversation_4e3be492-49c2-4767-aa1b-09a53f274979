vqa_path: 'datasets/vqa-checklist'
task_list:
  - name: vqa_casual
    config: 'configs/task/vqa_joint.yaml'
    trainer: 'train_vqa'
    json_files: ['datasets/vqa-checklist/joint_train.json' , 'datasets/vqa-checklist/joint_val.json' ,'datasets/vqa-checklist/joint_test.json' ]

# task order
order: fixed # fixed or random

# set pretrained as a file path or an url
pretrained: '/data/home/<USER>/checkpoints/model_base.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_base_capfilt_large.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_vqa.pth'
#pretrained: '/data/home/<USER>/checkpoints/model_base_vqa_capfilt_large.pth'



