import yaml
import os


def generate_configs():
    """
    Generates all necessary EQA configuration files.
    """
    # The script will create the files in its own directory.
    output_dir = os.path.dirname(os.path.abspath(__file__))

    # --- Base Definitions ---
    tasks = ["location", "color", "material", "state", "purpose", "relationship"]

    base_task_definitions = {
        task: {
            "json_files": {
                "train": f"datasets/eqa-checklist/train/{task}.json",
                "val": f"datasets/eqa-checklist/val/{task}.json",
                "test": f"datasets/eqa-checklist/test/{task}.json",
            }
        }
        for task in tasks
    }

    order_l2m = ["relationship", "material", "color", "location", "purpose", "state"]
    order_m2l = ["state", "purpose", "location", "color", "material", "relationship"]

    pretrained_models = {
        "base": ".../model_base.pth",
        "cap": ".../model_base_capfilt_large.pth",
    }

    # Helper to write yaml files
    def write_yaml(filepath, data):
        with open(filepath, "w") as f:
            yaml.dump(data, f, default_flow_style=False, sort_keys=False, indent=2)
        print(f"Generated: {filepath}")

    # --- Generate l2m and m2l files ---

    # 1. 6task_eqa_checklist_l2m.yaml
    config_l2m = {
        "task_list": {task: base_task_definitions[task] for task in order_l2m},
        "pretrained": pretrained_models["base"],
    }
    write_yaml(os.path.join(output_dir, "6task_eqa_checklist_l2m.yaml"), config_l2m)

    # 2. 6task_eqa_checklist_m2l.yaml
    config_m2l = {
        "task_list": {task: base_task_definitions[task] for task in order_m2l},
        "pretrained": pretrained_models["base"],
    }
    write_yaml(os.path.join(output_dir, "6task_eqa_checklist_m2l.yaml"), config_m2l)

    # 3. 6task_eqa_checklist_l2m_cap.yaml
    config_l2m_cap = {
        "task_list": {task: base_task_definitions[task] for task in order_l2m},
        "pretrained": pretrained_models["cap"],
    }
    write_yaml(
        os.path.join(output_dir, "6task_eqa_checklist_l2m_cap.yaml"), config_l2m_cap
    )

    # 4. 6task_eqa_checklist_m2l_cap.yaml
    config_m2l_cap = {
        "task_list": {task: base_task_definitions[task] for task in order_m2l},
        "pretrained": pretrained_models["cap"],
    }
    write_yaml(
        os.path.join(output_dir, "6task_eqa_checklist_m2l_cap.yaml"), config_m2l_cap
    )

    # --- Generate joint files ---

    joint_task_files = {
        "train": "datasets/eqa-checklist/joint_train.json",
        "val": "datasets/eqa-checklist/joint_val.json",
        "test": "datasets/eqa-checklist/joint_test.json",
    }

    # 5. joint.yaml
    config_joint = {
        "task_list": {"eqa_joint": {"json_files": joint_task_files}},
        "pretrained": pretrained_models["base"],
    }
    write_yaml(os.path.join(output_dir, "joint.yaml"), config_joint)

    # 6. joint_cap.yaml
    config_joint_cap = {
        "task_list": {"eqa_joint": {"json_files": joint_task_files}},
        "pretrained": pretrained_models["cap"],
    }
    write_yaml(os.path.join(output_dir, "joint_cap.yaml"), config_joint_cap)


if __name__ == "__main__":
    generate_configs()
    print("\nAll 6 EQA config files have been successfully generated.")
