task_list:
  state:
    json_files:
      train: datasets/eqa-checklist/train/state.json
      val: datasets/eqa-checklist/val/state.json
      test: datasets/eqa-checklist/test/state.json
  purpose:
    json_files:
      train: datasets/eqa-checklist/train/purpose.json
      val: datasets/eqa-checklist/val/purpose.json
      test: datasets/eqa-checklist/test/purpose.json
  location:
    json_files:
      train: datasets/eqa-checklist/train/location.json
      val: datasets/eqa-checklist/val/location.json
      test: datasets/eqa-checklist/test/location.json
  color:
    json_files:
      train: datasets/eqa-checklist/train/color.json
      val: datasets/eqa-checklist/val/color.json
      test: datasets/eqa-checklist/test/color.json
  material:
    json_files:
      train: datasets/eqa-checklist/train/material.json
      val: datasets/eqa-checklist/val/material.json
      test: datasets/eqa-checklist/test/material.json
  relationship:
    json_files:
      train: datasets/eqa-checklist/train/relationship.json
      val: datasets/eqa-checklist/val/relationship.json
      test: datasets/eqa-checklist/test/relationship.json
pretrained: '.../model_base.pth'
