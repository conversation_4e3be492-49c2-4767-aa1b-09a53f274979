import requests
import zipfile
import os
from tqdm import tqdm

def download_file(url, filename):
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        total_size = int(response.headers.get('content-length', 0))
        with open(filename, 'wb') as f, tqdm(
            desc=filename,
            total=total_size,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
        ) as bar:
            for data in response.iter_content(chunk_size=1024):
                size = f.write(data)
                bar.update(size)
        print(f"下载完成：{filename}")
    else:
        print("错误：无法下载文件")

def unzip_file(zip_filename, extract_to):
    with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
    print(f"解压完成：{zip_filename} 到 {extract_to}")

# 下载文件
train_zip = 'train2017.zip'
val_zip = 'val2017.zip'

download_file('http://images.cocodataset.org/zips/train2017.zip', train_zip)
download_file('http://images.cocodataset.org/zips/val2017.zip', val_zip)

# 创建解压目录
os.makedirs('train2017', exist_ok=True)
os.makedirs('val2017', exist_ok=True)

# 解压文件
unzip_file(train_zip, 'train2017')
unzip_file(val_zip, 'val2017')