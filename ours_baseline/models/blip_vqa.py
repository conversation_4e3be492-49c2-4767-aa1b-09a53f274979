#
# BLIP VQA 模型伪代码
#
# 1. 初始化 (BLIP_VQA.__init__)
#    - 创建视觉编码器 (Vision Transformer)
#    - 初始化文本分词器 (BERT Tokenizer)
#    - 创建文本编码器 (BERT Encoder)
#    - 创建文本解码器 (BERT LM Head)
#
# 2. 前向传播 (BLIP_VQA.forward)
#    - 2.1. 提取图像特征
#      - 输入: 图像
#      - 输出: image_embeds (图像嵌入)
#
#    - 2.2. 编码问题文本
#      - 输入: 问题文本
#      - 输出: question_tokens (编码后的问题)
#
#    - 2.3. 根据模式进行分支 (训练 vs. 推理)
#
#      - 2.3.1. 训练模式 (train=True)
#        - A. 标准训练 (非 ZSL)
#          - a. 预处理答案文本 (编码, 创建目标)
#          - b. 融合问题与图像: text_encoder(question, image_embeds) -> question_output
#          - c. 处理多答案: 复制 question_output 以匹配每个答案
#          - d. 计算生成损失: text_decoder(answer, question_output) -> loss
#          - e. (可选) 持续学习 (CL) 损失:
#            - CLS策略: 计算当前预测与 'slow'/'fast' 模式预测的KL散度
#            - LwF策略: 计算当前预测与过去任务模型预测的KL散度
#          - f. 返回总损失
#
#        - B. 零样本学习 (ZSL) 训练
#          - a. 预处理答案文本 (仅编码)
#          - b. 获取当前模型预测: prediction
#          - c. 获取 'teacher' (多任务ZSL) 模型预测: zsl_prediction
#          - d. 计算KL散度损失: KL(prediction, zsl_prediction)
#          - e. 返回损失
#
#      - 2.3.2. 推理模式 (train=False)
#        - A. 生成式推理 (inference='generate')
#          - a. 融合问题与图像: text_encoder(question, image_embeds) -> question_output
#          - b. 使用 beam search 生成答案序列: text_decoder.generate(...)
#          - c. 解码答案序列为文本
#          - d. 返回生成的答案列表
#
#        - B. 排序式推理 (调用 rank_answer)
#          - (此部分在 `forward` 中未直接实现, 而是通过外部调用 `rank_answer` 方法)
#
# 3. 答案排序 (BLIP_VQA.rank_answer)
#    - 3.1. 阶段一: 快速筛选
#      - a. 预测所有候选答案的第一个 token 的概率
#      - b. 为每个问题选择 top-k 个候选答案
#
#    - 3.2. 阶段二: 精细排序
#      - a. 计算 top-k 候选答案的完整序列的对数概率
#      - b. 为每个问题选择概率最高的答案
#      - c. 返回最佳答案的索引
#
# 4. 特征提取 (辅助函数, 主要用于持续学习)
#    - _get_task_feats_: 提取给定任务的多模态特征
#    - get_task_feats: 封装 _get_task_feats_, 负责任务切换
#    - _get_task_feats_cons_: 提取用于一致性学习的特征
#    - get_task_feats_cons: 封装 _get_task_feats_cons_
#
# 5. 辅助工具
#    - blip_vqa (工厂函数): 创建模型并加载预训练权重
#    - tile: 张量重复工具, 用于 beam search 等
#    - _KL_loss: 计算知识蒸馏的KL散度损失
#
# 导入必要的模型组件
from models.med import BertConfig, BertModel, BertLMHeadModel  # 多模态编码解码器
from models.blip import create_vit, init_tokenizer, load_checkpoint  # BLIP基础组件

# 导入深度学习框架和工具
import torch
from torch import nn
import torch.nn.functional as F
from transformers import BertTokenizer
import numpy as np


class BLIP_VQA(nn.Module):
    """
    BLIP视觉问答模型类

    这是一个基于BLIP架构的视觉问答模型，主要包含以下组件：
    1. 视觉编码器 (Visual Encoder): 使用Vision Transformer处理输入图像
    2. 文本编码器 (Text Encoder): 使用BERT编码问题文本
    3. 文本解码器 (Text Decoder): 使用BERT解码器生成答案

    支持的功能：
    - 标准VQA训练和推理
    - 持续学习 (Continual Learning)
    - 知识蒸馏 (Knowledge Distillation)
    - 零样本学习 (Zero-shot Learning)
    """

    def __init__(
        self,
        med_config="configs/med_config.json",  # 多模态编码解码器配置文件路径
        image_size=480,  # 输入图像尺寸
        vit="base",  # Vision Transformer模型大小
        vit_grad_ckpt=False,  # 是否使用梯度检查点节省显存
        vit_ckpt_layer=0,  # 梯度检查点层数
        agent=None,  # 持续学习智能体
    ):
        """
        初始化BLIP VQA模型

        参数说明:
            med_config (str): 多模态编码解码器配置文件路径
            image_size (int): 输入图像的尺寸大小
            vit (str): Vision Transformer模型规模 ('base', 'large'等)
            vit_grad_ckpt (bool): 是否启用梯度检查点以节省显存
            vit_ckpt_layer (int): 梯度检查点应用的层数
            agent: 持续学习代理，用于任务管理和知识保持
        """
        super().__init__()

        # ============================
        # 1. 初始化视觉编码器
        # ============================
        # 创建Vision Transformer作为视觉编码器，用于提取图像特征
        # vision_width返回视觉特征的维度
        self.visual_encoder, vision_width = create_vit(
            vit,
            image_size,
            vit_grad_ckpt,
            vit_ckpt_layer,
            drop_path_rate=0.1,
            agent=agent,
        )

        # ============================
        # 2. 初始化分词器
        # ============================
        # 初始化BERT分词器，用于处理文本输入（问题和答案）
        self.tokenizer = init_tokenizer()

        # ============================
        # 3. 初始化文本编码器
        # ============================
        # 加载编码器配置并设置视觉特征维度
        encoder_config = BertConfig.from_json_file(med_config)
        encoder_config.encoder_width = vision_width  # 设置跨模态注意力的视觉特征维度

        # 创建BERT编码器，用于编码问题文本并与视觉特征进行交互
        # add_pooling_layer=False 表示不添加池化层，保留序列输出
        self.text_encoder = BertModel(
            config=encoder_config, add_pooling_layer=False, agent=agent
        )

        # ============================
        # 4. 初始化文本解码器
        # ============================
        # 加载解码器配置
        decoder_config = BertConfig.from_json_file(med_config)

        # 创建BERT语言模型头，用于生成答案文本
        self.text_decoder = BertLMHeadModel(config=decoder_config, agent=agent)

    def forward(
        self,
        image,
        question,
        answer=None,
        n=None,
        weights=None,
        train=True,
        inference="generate",
        k_test=128,
        train_zsl=False,
        agent=None,
        wild_id=None,
    ):
        """
        BLIP VQA模型的前向传播函数

        这是模型的核心方法，根据不同的模式执行不同的操作：
        1. 训练模式：计算损失，支持标准训练、持续学习、知识蒸馏
        2. 推理模式：生成答案或对候选答案进行排序

        参数说明:
            image (torch.Tensor): 输入图像张量，形状为 [batch_size, 3, H, W]
            question (list): 问题文本列表，每个元素为一个问题字符串
            answer (list, optional): 答案文本列表，训练时使用
            n (list, optional): 每个问题对应的答案数量列表，用于多答案场景
            weights (torch.Tensor, optional): 每个答案的权重，用于加权损失计算
            train (bool): 是否为训练模式，True表示训练，False表示推理
            inference (str): 推理模式类型，'generate'表示生成式推理，'rank'表示排序式推理
            k_test (int): 推理时考虑的候选答案数量
            train_zsl (bool): 是否为零样本学习训练模式
            agent: 持续学习智能体，用于管理不同任务
            wild_id: 任务标识符，用于持续学习场景

        返回值:
            训练模式: 返回损失值 (torch.Tensor)
            推理模式: 返回生成的答案列表 (list) 或排序后的答案ID
        """

        # ============================
        # 1. 图像特征提取
        # ============================
        # 使用Vision Transformer提取图像特征
        # image_embeds形状: [batch_size, num_patches, vision_width]
        # 其中num_patches通常为(image_size/patch_size)^2 + 1 (包含CLS token)
        image_embeds = self.visual_encoder(image, wild_id=wild_id)

        # 创建图像注意力掩码，表示所有图像patch都是有效的
        # image_atts形状: [batch_size, num_patches]，全为1表示所有patch都参与注意力计算
        image_atts = torch.ones(image_embeds.size()[:-1], dtype=torch.long).to(
            image.device
        )

        # ============================
        # 2. 问题文本编码
        # ============================
        # 使用分词器对问题进行编码
        # padding='longest': 将batch内的序列填充到最长序列的长度
        # truncation=True: 如果序列长度超过max_length则截断
        # max_length=35: 问题的最大长度限制为35个token
        question = self.tokenizer(
            question,
            padding="longest",
            truncation=True,
            max_length=35,
            return_tensors="pt",
        ).to(image.device)

        # 将问题序列的第一个token设置为编码器特殊token
        # 这是BLIP模型的特殊设计，用于标识这是一个编码器输入序列
        question.input_ids[:, 0] = self.tokenizer.enc_token_id

        # ============================
        # 3. 模式分支：训练 vs 推理
        # ============================
        if train:
            # ============================
            # 3.1 标准训练模式
            # ============================
            if not train_zsl:
                """
                标准训练流程：
                1. 对答案进行编码和预处理
                2. 使用文本编码器处理问题并与图像特征融合
                3. 准备解码器输入（处理多答案情况）
                4. 使用文本解码器计算答案生成损失
                5. 可选：添加持续学习的知识蒸馏损失
                """

                # -------------------------
                # 3.1.1 答案文本预处理
                # -------------------------
                # n: 每个问题对应的答案数量列表，例如 [2, 3, 1] 表示3个问题分别有2、3、1个答案
                # weights: 每个答案的权重，用于加权损失计算，处理答案重要性不同的情况

                # 对答案文本进行分词编码
                answer = self.tokenizer(
                    answer, padding="longest", return_tensors="pt"
                ).to(image.device)

                # 将答案序列的第一个token设置为BOS（Begin of Sequence）token
                # 这标识答案序列的开始，是解码器生成的起始信号
                answer.input_ids[:, 0] = self.tokenizer.bos_token_id

                # 创建答案的目标标签，用于计算损失
                # 将padding的token（pad_token_id）替换为-100，这样在计算交叉熵损失时会被忽略
                answer_targets = answer.input_ids.masked_fill(
                    answer.input_ids == self.tokenizer.pad_token_id, -100
                )

                # -------------------------
                # 3.1.2 问题编码与视觉融合
                # -------------------------
                # 使用文本编码器处理问题，同时融合图像特征
                # 这是跨模态注意力的关键步骤，让问题理解能够结合视觉信息
                question_output = self.text_encoder(
                    question.input_ids,
                    attention_mask=question.attention_mask,  # 问题的注意力掩码
                    encoder_hidden_states=image_embeds,  # 图像特征作为编码器隐藏状态
                    encoder_attention_mask=image_atts,  # 图像的注意力掩码
                    return_dict=True,
                )

                # -------------------------
                # 3.1.3 处理多答案情况
                # -------------------------
                # 由于一个问题可能有多个正确答案，需要将问题表示复制相应次数
                # 例如：如果问题1有2个答案，问题2有3个答案，则需要将问题1的表示复制2次，问题2的表示复制3次
                question_states = []
                question_atts = []
                for b, i in enumerate(n):  # b是batch索引，i是该问题的答案数量
                    # 将第b个问题的最后一层隐藏状态复制i次
                    question_states += [question_output.last_hidden_state[b]] * i
                    # 将第b个问题的注意力掩码也复制i次
                    question_atts += [question.attention_mask[b]] * i

                # 将列表转换为张量，现在每个答案都有对应的问题表示
                question_states = torch.stack(question_states, 0)
                question_atts = torch.stack(question_atts, 0)

                # -------------------------
                # 3.1.4 答案生成和损失计算
                # -------------------------
                # 使用文本解码器生成答案，这是一个条件语言建模任务
                # 条件是融合了视觉信息的问题表示
                answer_output = self.text_decoder(
                    answer.input_ids,
                    attention_mask=answer.attention_mask,  # 答案的注意力掩码
                    encoder_hidden_states=question_states,  # 问题状态作为条件
                    encoder_attention_mask=question_atts,  # 问题的注意力掩码
                    labels=answer_targets,  # 目标标签用于计算损失
                    return_dict=True,
                    reduction="none",  # 不进行reduction，保持每个样本的损失
                )

                # 计算加权损失：将每个答案的损失乘以对应的权重
                # weights反映了不同答案的重要性或置信度
                loss = weights * answer_output.loss

                # 对所有答案的损失求和，然后除以batch size得到平均损失
                loss = loss.sum() / image.size(0)

                # ============================
                # 3.1.5 持续学习：知识蒸馏策略
                # ============================

                # -------------------------
                # 3.1.5.1 CLS (Continual Learning Strategy) 方法
                # -------------------------
                if agent.cls and agent.task_id != 0:
                    """
                    CLS持续学习策略：
                    通过知识蒸馏防止灾难性遗忘，使用'slow'和'fast'两种不同的融合模式
                    - slow模式：使用完整的跨模态注意力机制
                    - fast模式：使用简化的特征融合方式
                    目标：让当前任务的预测与过去任务保持一致性
                    """
                    with torch.no_grad():  # 蒸馏过程不需要梯度更新
                        # 保存当前任务的预测结果
                        prediction = answer_output.logits
                        # 语言建模需要进行位移：预测下一个token
                        prediction = prediction[:, :-1, :].contiguous()

                        # 保存原始的融合类型
                        fuse_type = agent.fuse_type

                        # ----------------------
                        # 获取'slow'模式下的预测
                        # ----------------------
                        agent.fuse_type = "slow"

                        # 重新进行前向传播，使用slow融合模式
                        image_embeds = self.visual_encoder(image)
                        image_atts = torch.ones(
                            image_embeds.size()[:-1], dtype=torch.long
                        ).to(image.device)

                        question_output = self.text_encoder(
                            question.input_ids,
                            attention_mask=question.attention_mask,
                            encoder_hidden_states=image_embeds,
                            encoder_attention_mask=image_atts,
                            return_dict=True,
                        )

                        # 处理多答案情况
                        question_states = []
                        question_atts = []
                        for b, i in enumerate(n):
                            question_states += [
                                question_output.last_hidden_state[b]
                            ] * i
                            question_atts += [question.attention_mask[b]] * i
                        question_states = torch.stack(question_states, 0)
                        question_atts = torch.stack(question_atts, 0)

                        # 获取slow模式下的答案预测
                        answer_output = self.text_decoder(
                            answer.input_ids,
                            attention_mask=answer.attention_mask,
                            encoder_hidden_states=question_states,
                            encoder_attention_mask=question_atts,
                            labels=None,  # 不计算损失
                            return_dict=True,
                            reduction="none",
                        )

                        slow_prediction = answer_output.logits
                        slow_prediction = slow_prediction[:, :-1, :].contiguous()

                        # ----------------------
                        # 获取'fast'模式下的预测
                        # ----------------------
                        agent.fuse_type = "fast"

                        # 重新进行前向传播，使用fast融合模式
                        image_embeds = self.visual_encoder(image)
                        image_atts = torch.ones(
                            image_embeds.size()[:-1], dtype=torch.long
                        ).to(image.device)

                        question_output = self.text_encoder(
                            question.input_ids,
                            attention_mask=question.attention_mask,
                            encoder_hidden_states=image_embeds,
                            encoder_attention_mask=image_atts,
                            return_dict=True,
                        )

                        # 处理多答案情况
                        question_states = []
                        question_atts = []
                        for b, i in enumerate(n):
                            question_states += [
                                question_output.last_hidden_state[b]
                            ] * i
                            question_atts += [question.attention_mask[b]] * i
                        question_states = torch.stack(question_states, 0)
                        question_atts = torch.stack(question_atts, 0)

                        # 获取fast模式下的答案预测
                        answer_output = self.text_decoder(
                            answer.input_ids,
                            attention_mask=answer.attention_mask,
                            encoder_hidden_states=question_states,
                            encoder_attention_mask=question_atts,
                            labels=None,  # 不计算损失
                            return_dict=True,
                            reduction="none",
                        )

                        fast_prediction = answer_output.logits
                        fast_prediction = fast_prediction[:, :-1, :].contiguous()

                        # 恢复原始融合类型
                        agent.fuse_type = fuse_type

                    # 添加知识蒸馏损失：当前预测与slow/fast模式预测的KL散度
                    # 这有助于保持模型在不同融合模式下的一致性，防止灾难性遗忘
                    loss += _KL_loss(prediction, slow_prediction, 1) + _KL_loss(
                        prediction, fast_prediction, 1
                    )

                # -------------------------
                # 3.1.5.2 LwF (Learning without Forgetting) 方法
                # -------------------------
                elif agent.LwF and agent.task_id != 0:
                    """
                    LwF (Learning without Forgetting) 持续学习策略：
                    通过保持与过去任务预测的一致性来防止灾难性遗忘
                    使用当前模型与过去任务模型的预测进行知识蒸馏
                    """
                    with torch.no_grad():  # 蒸馏过程不需要梯度更新
                        # 保存当前任务的预测结果
                        prediction = answer_output.logits
                        prediction = prediction[:, :-1, :].contiguous()

                        # 保存原始的融合类型
                        fuse_type = agent.fuse_type

                        # ----------------------
                        # 获取过去任务模式下的预测
                        # ----------------------
                        agent.fuse_type = "LwF"

                        # 使用LwF模式重新进行前向传播
                        image_embeds = self.visual_encoder(image)
                        image_atts = torch.ones(
                            image_embeds.size()[:-1], dtype=torch.long
                        ).to(image.device)

                        question_output = self.text_encoder(
                            question.input_ids,
                            attention_mask=question.attention_mask,
                            encoder_hidden_states=image_embeds,
                            encoder_attention_mask=image_atts,
                            return_dict=True,
                        )

                        # 处理多答案情况
                        question_states = []
                        question_atts = []
                        for b, i in enumerate(n):
                            question_states += [
                                question_output.last_hidden_state[b]
                            ] * i
                            question_atts += [question.attention_mask[b]] * i
                        question_states = torch.stack(question_states, 0)
                        question_atts = torch.stack(question_atts, 0)

                        # 获取过去任务模式下的答案预测
                        answer_output = self.text_decoder(
                            answer.input_ids,
                            attention_mask=answer.attention_mask,
                            encoder_hidden_states=question_states,
                            encoder_attention_mask=question_atts,
                            labels=None,  # 不计算损失
                            return_dict=True,
                            reduction="none",
                        )

                        past_prediction = answer_output.logits
                        past_prediction = past_prediction[:, :-1, :].contiguous()

                    # 添加LwF知识蒸馏损失：当前预测与过去任务预测的KL散度
                    # 这确保模型在学习新任务时不会忘记过去任务的知识
                    loss += _KL_loss(prediction, past_prediction, 1)

                    # 恢复原始融合类型
                    agent.fuse_type = fuse_type

                return loss

            # ============================
            # 3.2 零样本学习训练模式 (Zero-Shot Learning)
            # ============================
            else:
                """
                零样本学习训练流程：
                1. 处理答案编码（不计算标准损失）
                2. 获取当前任务的预测结果
                3. 通过知识蒸馏与多任务零样本模式保持一致性
                4. 使用KL散度作为训练目标
                """

                # -------------------------
                # 3.2.1 答案预处理（零样本模式）
                # -------------------------
                answer = self.tokenizer(
                    answer, padding="longest", return_tensors="pt"
                ).to(image.device)
                answer.input_ids[:, 0] = self.tokenizer.bos_token_id
                # 注意：零样本学习模式下不创建answer_targets，因为不计算标准的交叉熵损失

                # 获取损失权重系数，用于平衡不同损失项
                alpha = agent.args.loss_alpha

                # -------------------------
                # 3.2.2 问题编码与预测生成
                # -------------------------
                # 使用文本编码器处理问题，融合视觉信息
                question_output = self.text_encoder(
                    question.input_ids,
                    attention_mask=question.attention_mask,
                    encoder_hidden_states=image_embeds,
                    encoder_attention_mask=image_atts,
                    return_dict=True,
                    wild_id=wild_id,
                )

                # 处理多答案情况：为每个答案复制对应的问题表示
                question_states = []
                question_atts = []
                for b, i in enumerate(n):
                    question_states += [question_output.last_hidden_state[b]] * i
                    question_atts += [question.attention_mask[b]] * i
                question_states = torch.stack(question_states, 0)
                question_atts = torch.stack(question_atts, 0)

                # 获取当前模式下的答案预测（不计算损失）
                answer_output = self.text_decoder(
                    answer.input_ids,
                    attention_mask=answer.attention_mask,
                    encoder_hidden_states=question_states,
                    encoder_attention_mask=question_atts,
                    labels=None,  # 零样本模式不使用标准损失
                    return_dict=True,
                    reduction="none",
                    wild_id=wild_id,
                )

                # 获取当前任务的预测logits
                prediction = answer_output.logits
                # 进行位移以适应next-token prediction的设置
                prediction = prediction[:, :-1, :].contiguous()

                # -------------------------
                # 3.2.3 多任务零样本蒸馏
                # -------------------------
                if agent.train_distill_type == "multi-zsl-single":
                    """
                    多任务零样本单一蒸馏策略：
                    使用专门的多任务零样本模式获取teacher预测
                    然后与当前预测进行知识蒸馏
                    """
                    with torch.no_grad():  # teacher预测不需要梯度
                        # 保存原始融合类型
                        fuse_type = agent.fuse_type
                        # 切换到多任务零样本模式
                        agent.fuse_type = "multi-zsl-single"

                        # 使用多任务零样本模式重新进行前向传播
                        image_embeds = self.visual_encoder(image, wild_id=wild_id)
                        image_atts = torch.ones(
                            image_embeds.size()[:-1], dtype=torch.long
                        ).to(image.device)

                        question_output = self.text_encoder(
                            question.input_ids,
                            attention_mask=question.attention_mask,
                            encoder_hidden_states=image_embeds,
                            encoder_attention_mask=image_atts,
                            return_dict=True,
                            wild_id=wild_id,
                        )

                        # 处理多答案情况
                        question_states = []
                        question_atts = []
                        for b, i in enumerate(n):
                            question_states += [
                                question_output.last_hidden_state[b]
                            ] * i
                            question_atts += [question.attention_mask[b]] * i
                        question_states = torch.stack(question_states, 0)
                        question_atts = torch.stack(question_atts, 0)

                        # 获取teacher模式下的预测结果
                        answer_output = self.text_decoder(
                            answer.input_ids,
                            attention_mask=answer.attention_mask,
                            encoder_hidden_states=question_states,
                            encoder_attention_mask=question_atts,
                            labels=None,  # 不计算损失
                            return_dict=True,
                            reduction="none",
                            wild_id=wild_id,
                        )

                        zsl_prediction = answer_output.logits
                        zsl_prediction = zsl_prediction[:, :-1, :].contiguous()

                # 恢复原始融合类型
                agent.fuse_type = fuse_type

                # 计算知识蒸馏损失：当前预测与零样本teacher预测的KL散度
                # alpha用于控制蒸馏损失的权重
                loss = alpha * _KL_loss(prediction, zsl_prediction, 1)

                return loss

        # ============================
        # 4. 推理模式
        # ============================
        else:
            """
            推理模式流程：
            1. 使用文本编码器处理问题并融合视觉信息
            2. 根据推理类型执行不同策略：
               - 'generate': 生成式推理，使用beam search生成答案
               - 其他: 排序式推理，对候选答案进行排序选择
            """

            # -------------------------
            # 4.1 问题编码与视觉融合
            # -------------------------
            # 推理时只需要编码问题，不需要处理答案
            question_output = self.text_encoder(
                question.input_ids,
                attention_mask=question.attention_mask,
                encoder_hidden_states=image_embeds,  # 融合视觉特征
                encoder_attention_mask=image_atts,  # 视觉注意力掩码
                return_dict=True,
            )

            # -------------------------
            # 4.2 生成式推理
            # -------------------------
            if inference == "generate":
                """
                使用beam search进行答案生成：
                1. 设置beam search参数
                2. 准备解码器输入
                3. 生成答案序列
                4. 解码为文本并返回
                """
                # beam search的束宽，影响生成质量和多样性
                num_beams = 3

                # 为beam search复制question states
                # repeat_interleave确保每个问题有num_beams个副本用于parallel beam search
                question_states = question_output.last_hidden_state
                # .repeat_interleave(
                #     num_beams, dim=0
                # )
                question_atts = torch.ones(
                    question_states.size()[:-1], dtype=torch.long
                ).to(question_states.device)

                # 准备传递给生成器的参数
                model_kwargs = {
                    "encoder_hidden_states": question_states,
                    "encoder_attention_mask": question_atts,
                }

                # 创建BOS token作为生成的起始
                bos_ids = torch.full(
                    (image.size(0), 1),
                    fill_value=self.tokenizer.bos_token_id,
                    device=image.device,
                )

                # 使用BERT解码器进行beam search生成
                outputs = self.text_decoder.generate(
                    input_ids=bos_ids,
                    max_length=10,  # 最大生成长度
                    min_length=1,  # 最小生成长度
                    num_beams=num_beams,  # beam search宽度
                    eos_token_id=self.tokenizer.sep_token_id,  # 结束符
                    pad_token_id=self.tokenizer.pad_token_id,  # 填充符
                    **model_kwargs
                )

                # 将生成的token序列解码为文本答案
                answers = []
                for output in outputs:
                    # skip_special_tokens=True 会跳过特殊token（如BOS、EOS、PAD等）
                    answer = self.tokenizer.decode(output, skip_special_tokens=True)
                    answers.append(answer)
                return answers

    def rank_answer(self, question_states, question_atts, answer_ids, answer_atts, k):
        """
        答案排序方法：对候选答案进行排序，选择最佳答案

        这是一个两阶段的答案选择过程：
        1. 第一阶段：根据第一个token的概率进行初步筛选，选择top-k候选
        2. 第二阶段：对top-k候选进行完整的序列概率计算，选择最优答案

        参数说明:
            question_states (torch.Tensor): 问题的隐藏状态表示，形状 [num_questions, seq_len, hidden_size]
            question_atts (torch.Tensor): 问题的注意力掩码，形状 [num_questions, seq_len]
            answer_ids (torch.Tensor): 候选答案的token ID，形状 [num_answers, answer_len]
            answer_atts (torch.Tensor): 候选答案的注意力掩码，形状 [num_answers, answer_len]
            k (int): 每个问题考虑的top-k候选答案数量

        返回值:
            max_ids (torch.Tensor): 每个问题选择的最佳答案在候选集中的索引
        """

        # -------------------------
        # 阶段1：基于第一个token的快速筛选
        # -------------------------
        num_ques = question_states.size(0)

        # 创建BOS token作为解码起始，为每个问题复制一份
        start_ids = answer_ids[0, 0].repeat(num_ques, 1)  # bos token

        # 使用解码器预测第一个token的概率分布
        start_output = self.text_decoder(
            start_ids,
            encoder_hidden_states=question_states,  # 问题作为条件
            encoder_attention_mask=question_atts,  # 问题的注意力掩码
            return_dict=True,
            reduction="none",
        )

        # 获取第一个位置的logits（即预测第一个答案token的概率）
        logits = start_output.logits[:, 0, :]  # first token's logit

        # -------------------------
        # 计算候选答案第一个token的概率
        # -------------------------
        # 获取所有候选答案的第一个实际token（跳过BOS token）
        answer_first_token = answer_ids[:, 1]

        # 计算softmax概率并选择对应候选答案第一个token的概率
        prob_first_token = F.softmax(logits, dim=1).index_select(
            dim=1, index=answer_first_token
        )

        # 对每个问题选择概率最高的k个候选答案
        # topk_probs: top-k概率值，形状 [num_questions, k]
        # topk_ids: top-k答案在候选集中的索引，形状 [num_questions, k]
        topk_probs, topk_ids = prob_first_token.topk(k, dim=1)

        # -------------------------
        # 阶段2：对top-k候选进行完整序列评估
        # -------------------------
        # 为每个问题的top-k候选准备输入序列
        input_ids = []
        input_atts = []
        for b, topk_id in enumerate(topk_ids):
            # 根据topk_id索引选择对应的答案序列
            input_ids.append(answer_ids.index_select(dim=0, index=topk_id))
            input_atts.append(answer_atts.index_select(dim=0, index=topk_id))

        # 将所有问题的候选答案concatenate成一个大batch
        # 形状: [num_questions * k, answer_len]
        input_ids = torch.cat(input_ids, dim=0)
        input_atts = torch.cat(input_atts, dim=0)

        # 创建目标标签：将padding token替换为-100以在损失计算中忽略
        targets_ids = input_ids.masked_fill(
            input_ids == self.tokenizer.pad_token_id, -100
        )

        # 为了匹配候选答案的数量，需要重复问题状态
        # tile函数将question_states在第0维重复k次
        question_states = tile(question_states, 0, k)
        question_atts = tile(question_atts, 0, k)

        # -------------------------
        # 计算完整序列的负对数似然
        # -------------------------
        # 使用解码器计算每个候选答案的完整序列概率
        output = self.text_decoder(
            input_ids,
            attention_mask=input_atts,  # 答案注意力掩码
            encoder_hidden_states=question_states,  # 重复后的问题状态
            encoder_attention_mask=question_atts,  # 重复后的问题注意力掩码
            labels=targets_ids,  # 目标标签
            return_dict=True,
            reduction="none",
        )

        # 获取负对数似然损失的负值，即对数概率
        log_probs_sum = -output.loss

        # 重新整理为 [num_questions, k] 的形状
        log_probs_sum = log_probs_sum.view(num_ques, k)

        # -------------------------
        # 选择最优答案
        # -------------------------
        # 对每个问题，选择对数概率最大的候选答案
        max_topk_ids = log_probs_sum.argmax(dim=1)

        # 将局部索引转换为全局索引：从top-k中的索引转换为原始候选集中的索引
        max_ids = topk_ids[max_topk_ids >= 0, max_topk_ids]

        return max_ids

    def _get_task_feats_(
        self,
        image,
        text_input_ids,
        text_attention_mask,
        answer,
        answer_targets,
        n,
        no_detach=False,
        text_is_emb=False,
    ):
        """
        内部方法：提取特定任务的特征表示

        这个方法是get_task_feats的核心实现，用于提取给定任务的多模态特征表示。
        主要用于持续学习中的任务特征提取和对比学习。

        参数说明:
            image (torch.Tensor): 输入图像张量
            text_input_ids (torch.Tensor): 文本输入的token ID或嵌入表示
            text_attention_mask (torch.Tensor): 文本的注意力掩码
            answer (torch.Tensor): 答案的token化结果
            answer_targets (torch.Tensor): 答案的目标标签
            n (list): 每个问题对应的答案数量
            no_detach (bool): 是否保留梯度，False表示detach不计算梯度
            text_is_emb (bool): text_input_ids是否已经是嵌入表示

        返回值:
            answer_output: 解码器的输出，包含特征表示
        """

        # -------------------------
        # 1. 视觉特征提取
        # -------------------------
        # 使用视觉编码器提取图像特征
        image_embeds_q = self.visual_encoder(image)
        if not no_detach:
            # 在不需要梯度的情况下detach，常用于蒸馏或对比学习
            image_embeds_q = image_embeds_q.detach()

        # 创建图像注意力掩码
        image_atts_q = torch.ones(image_embeds_q.size()[:-1], dtype=torch.long).to(
            image.device
        )

        # -------------------------
        # 2. 文本编码与多模态融合
        # -------------------------
        # 根据text_is_emb参数决定输入类型：token ID或已有的嵌入
        question_output = self.text_encoder(
            text_input_ids if (not text_is_emb) else None,  # token ID输入
            encoder_embeds=(text_input_ids if text_is_emb else None),  # 嵌入输入
            attention_mask=text_attention_mask,  # 文本注意力掩码
            encoder_hidden_states=image_embeds_q,  # 图像特征用于跨模态注意力
            encoder_attention_mask=image_atts_q,  # 图像注意力掩码
            return_dict=True,
        )
        if not no_detach:
            # 在不需要梯度的情况下detach
            question_output = question_output.detach()

        # -------------------------
        # 3. 图像特征池化（可选）
        # -------------------------
        # 对图像特征进行平均池化，得到全局图像表示
        image_embeds_q = image_embeds_q.mean(dim=1)
        if not no_detach:
            image_embeds_q = image_embeds_q.detach()

        # -------------------------
        # 4. 处理多答案情况
        # -------------------------
        # 为每个答案复制对应的问题表示
        question_states = []
        question_atts = []
        for b, i in enumerate(n):
            question_states += [question_output.last_hidden_state[b]] * i
            # 注意：这里应该使用text_attention_mask而不是text_attention_mask.attention_mask
            question_atts += [text_attention_mask.attention_mask[b]] * i
        question_states = torch.stack(question_states, 0)
        question_atts = torch.stack(question_atts, 0)

        # -------------------------
        # 5. 答案解码
        # -------------------------
        # 使用文本解码器处理答案，获取特征表示
        answer_output = self.text_decoder(
            answer.input_ids,
            attention_mask=answer.attention_mask,
            encoder_hidden_states=question_states,
            encoder_attention_mask=question_atts,
            labels=None,  # 不计算损失，只提取特征
            return_dict=True,
            reduction="none",
        )

        return answer_output

    def get_task_feats(
        self,
        image,
        text_input_ids,
        text_attention_mask,
        task_id,
        agent,
        answer,
        answer_targets,
        n,
        no_detach=False,
        text_is_emb=False,
    ):
        """
        获取特定任务的特征表示（公共接口）

        这是一个封装方法，用于安全地切换到指定任务并提取特征，
        常用于持续学习场景中的任务间特征对比。

        参数说明:
            image (torch.Tensor): 输入图像
            text_input_ids (torch.Tensor): 文本输入（token ID或嵌入）
            text_attention_mask (torch.Tensor): 文本注意力掩码
            task_id (int): 目标任务ID
            agent: 持续学习智能体
            answer, answer_targets, n: 答案相关参数
            no_detach (bool): 是否保留梯度
            text_is_emb (bool): 文本输入是否为嵌入表示

        返回值:
            text_embeds_q: 提取的任务特征
        """

        # -------------------------
        # 任务切换和特征提取
        # -------------------------
        # 切换到指定任务的模型状态
        agent.prep_model4task(task_id, force=True)

        if not no_detach:
            # 无梯度模式下提取特征（用于对比学习等）
            with torch.no_grad():
                text_embeds_q = self._get_task_feats_(
                    image,
                    text_input_ids,
                    text_attention_mask,
                    answer,
                    answer_targets,
                    n,
                    no_detach,
                    text_is_emb,
                )
        else:
            # 保留梯度模式下提取特征（用于端到端训练）
            text_embeds_q = self._get_task_feats_(
                image,
                text_input_ids,
                text_attention_mask,
                answer,
                answer_targets,
                n,
                no_detach,
                text_is_emb,
            )

        # 恢复到默认任务状态
        agent.prep_model4task(-1)
        return text_embeds_q

    def get_task_feats_cons(
        self,
        image,
        text_input_ids,
        text_attention_mask,
        task_id,
        agent,
        answer,
        answer_targets,
        n,
        no_detach=False,
        text_is_emb=False,
    ):
        """
        获取任务特征的一致性版本

        这是get_task_feats的变体，专门用于一致性学习和对比学习场景。
        与标准版本的主要区别在于损失计算和注意力掩码的处理。

        参数说明: (与get_task_feats相同)

        返回值:
            text_embeds_q: 用于一致性学习的任务特征
        """

        # 任务切换和特征提取（与get_task_feats相同的模式）
        agent.prep_model4task(task_id, force=True)
        if not no_detach:
            with torch.no_grad():
                text_embeds_q = self._get_task_feats_cons(
                    image,
                    text_input_ids,
                    text_attention_mask,
                    answer,
                    answer_targets,
                    n,
                    no_detach,
                    text_is_emb,
                )
        else:
            text_embeds_q = self._get_task_feats_cons(
                image,
                text_input_ids,
                text_attention_mask,
                answer,
                answer_targets,
                n,
                no_detach,
                text_is_emb,
            )
        agent.prep_model4task(-1)
        return text_embeds_q

    def _get_task_feats_cons(
        self,
        image,
        text_input_ids,
        text_attention_mask,
        answer,
        answer_targets,
        n,
        no_detach=False,
        text_is_emb=False,
    ):
        """
        内部方法：提取一致性学习的任务特征

        与_get_task_feats_的主要区别：
        1. 在解码器中使用answer_targets计算损失
        2. 注意力掩码处理方式不同
        3. 专门用于一致性约束的特征提取

        参数说明: (与_get_task_feats_相同)

        返回值:
            answer_output: 包含一致性约束的解码器输出
        """

        # 视觉特征提取（与标准版本相同）
        image_embeds_q = self.visual_encoder(image)
        if not no_detach:
            image_embeds_q = image_embeds_q.detach()
        image_atts_q = torch.ones(image_embeds_q.size()[:-1], dtype=torch.long).to(
            image.device
        )

        # 文本编码与多模态融合（与标准版本相同）
        question_output = self.text_encoder(
            text_input_ids if (not text_is_emb) else None,
            encoder_embeds=(text_input_ids if text_is_emb else None),
            attention_mask=text_attention_mask,
            encoder_hidden_states=image_embeds_q,
            encoder_attention_mask=image_atts_q,
            return_dict=True,
        )
        if not no_detach:
            question_output = question_output.detach()

        # 图像特征池化（与标准版本相同）
        image_embeds_q = image_embeds_q.mean(dim=1)
        if not no_detach:
            image_embeds_q = image_embeds_q.detach()

        # 处理多答案情况
        question_states = []
        question_atts = []
        for b, i in enumerate(n):
            question_states += [question_output.last_hidden_state[b]] * i
            # 关键区别：这里直接使用text_attention_mask而不是.attention_mask属性
            question_atts += [text_attention_mask[b]] * i
        question_states = torch.stack(question_states, 0)
        question_atts = torch.stack(question_atts, 0)

        # 答案解码（关键区别：使用answer_targets计算损失）
        answer_output = self.text_decoder(
            answer.input_ids,
            attention_mask=answer.attention_mask,
            encoder_hidden_states=question_states,
            encoder_attention_mask=question_atts,
            labels=answer_targets,  # 与标准版本不同：这里计算损失
            return_dict=True,
            reduction="none",
        )

        return answer_output


# ================================================================================================
# 模型构造和辅助函数
# ================================================================================================


def blip_vqa(pretrained="", **kwargs):
    """
    BLIP VQA模型工厂函数

    这是创建BLIP VQA模型的主要接口函数，负责模型实例化和预训练权重加载。

    参数说明:
        pretrained (str): 预训练模型的路径，如果为空则不加载预训练权重
        **kwargs: 传递给BLIP_VQA构造函数的其他参数

    返回值:
        model (BLIP_VQA): 初始化后的BLIP VQA模型实例
        head_not_loaded (bool): 是否缺少分类头的权重
            - True: 分类头权重未正确加载，需要重新训练
            - False: 所有权重都已正确加载
    """

    # -------------------------
    # 1. 模型实例化
    # -------------------------
    # 使用传入的参数创建BLIP VQA模型实例
    model = BLIP_VQA(**kwargs)

    # -------------------------
    # 2. 预训练权重加载
    # -------------------------
    head_not_loaded = True  # 默认假设分类头未加载

    if pretrained:
        # 加载预训练检查点
        model, msg = load_checkpoint(model, pretrained)

        # 打印缺失的权重键，用于调试
        # assert(len(msg.missing_keys)==0)  # 注释掉的断言，允许部分权重缺失
        print("missing keys:")
        print(msg.missing_keys)

        # 初始假设所有权重都已加载
        head_not_loaded = False

        # 检查是否缺少关键的分类头权重
        for k in msg.missing_keys:
            if "text_decoder.cls" in k:
                # 如果缺少文本解码器的分类层权重，标记为未加载
                head_not_loaded = True

    return model, head_not_loaded
    # return model  # 注释掉的简化返回


def tile(x, dim, n_tile):
    """
    张量平铺（重复）函数

    这个函数用于在指定维度上重复张量，常用于beam search或处理多候选答案的场景。
    与torch.repeat不同，这个函数保持原始元素的相对顺序。

    示例:
        如果 x = [[a], [b], [c]]，dim=0，n_tile=2
        结果为 [[a], [a], [b], [b], [c], [c]]
        而不是 [[a], [b], [c], [a], [b], [c]]

    参数说明:
        x (torch.Tensor): 输入张量
        dim (int): 要重复的维度
        n_tile (int): 重复次数

    返回值:
        torch.Tensor: 重复后的张量
    """

    # 获取指定维度的原始大小
    init_dim = x.size(dim)

    # 创建重复模式：在指定维度重复n_tile次，其他维度保持不变
    repeat_idx = [1] * x.dim()  # 初始化为全1的重复次数列表
    repeat_idx[dim] = n_tile  # 在指定维度设置重复次数

    # 使用repeat进行初步重复
    x = x.repeat(*(repeat_idx))

    # 创建重新排序的索引，确保相邻元素来自同一个原始元素
    # 例如：[0, 0, 1, 1, 2, 2] 而不是 [0, 1, 2, 0, 1, 2]
    order_index = torch.LongTensor(
        np.concatenate([init_dim * np.arange(n_tile) + i for i in range(init_dim)])
    )

    # 使用索引重新排序
    return torch.index_select(x, dim, order_index.to(x.device))


def _KL_loss(pred, soft, T):
    """
    计算KL散度损失（知识蒸馏损失）

    这个函数计算学生网络预测(pred)和教师网络预测(soft)之间的KL散度，
    是知识蒸馏和持续学习中的关键损失函数。

    KL散度公式: KL(P||Q) = Σ P(x) * log(P(x)/Q(x))
    在知识蒸馏中：P是教师网络的软标签，Q是学生网络的预测

    参数说明:
        pred (torch.Tensor): 学生网络的预测logits，形状 [..., vocab_size]
        soft (torch.Tensor): 教师网络的预测logits，形状与pred相同
        T (float): 温度参数，用于软化概率分布
            - T > 1: 使分布更平滑，关注相似类别间的关系
            - T = 1: 标准softmax
            - T < 1: 使分布更尖锐，更关注最高概率的类别

    返回值:
        torch.Tensor: KL散度损失值（标量）

    数学原理:
        1. 对预测logits应用温度缩放：logits / T
        2. 计算学生网络的log softmax：log_softmax(pred/T)
        3. 计算教师网络的softmax：softmax(soft/T)
        4. 计算KL散度：KLDiv(log_softmax(pred/T), softmax(soft/T))
        5. 乘以 T² 进行温度补偿
    """

    # 注释掉的简单实现方式：
    # pred = torch.log_softmax(pred / T, dim=1)
    # soft = torch.softmax(soft / T, dim=1)

    # 计算温度缩放后的KL散度损失
    # reduction='batchmean'：对batch维度求平均
    loss = (
        nn.KLDivLoss(reduction="batchmean")(
            torch.log_softmax(pred / T, dim=-1),  # 学生网络：log softmax
            torch.softmax(soft / T, dim=-1),  # 教师网络：softmax
        )
        * T
        * T
    )  # 温度补偿：T²

    return loss
