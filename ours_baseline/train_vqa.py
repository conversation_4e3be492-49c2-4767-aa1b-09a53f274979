"""
VQA (Visual Question Answering) 训练脚本
改编自以下版权代码:
* Copyright (c) 2022, salesforce.com, inc.
* All rights reserved.
* SPDX-License-Identifier: BSD-3-Clause
* For full license text, see LICENSE.txt file in the repo root or https://opensource.org/licenses/BSD-3-Clause
* By Junnan Li

============================================================================
伪代码 (Pseudocode):
============================================================================

程序 VQA_训练系统:

函数 train(模型, 数据加载器, 优化器, 轮次, 设备, 配置, 智能体):
    设置模型为训练模式
    初始化指标记录器(学习率, 损失)

    对于 数据加载器中的每个批次(图像, 问题, 答案, 权重, 数量):
        将图像和权重移动到设备
        计算损失 = 模型前向传播(图像, 问题, 答案, 训练=True, 权重, 智能体)
        清零梯度
        反向传播(损失)
        更新参数(优化器)
        记录指标(学习率, 损失)

    同步进程间统计信息
    返回 训练统计信息

函数 train_zsl(模型, 常规数据, 零样本数据, 其他参数):
    设置模型为训练模式
    初始化指标记录器(学习率, 总损失, 零样本损失)

    对于 同时遍历(常规数据, 零样本数据):
        如果 批次数据为空: 跳出循环

        # 处理常规训练数据
        计算常规损失 = 模型前向传播(常规数据)

        # 处理零样本数据
        如果 智能体需要零样本学习:
            初始化零样本损失 = 0
            按照wild_id分组零样本数据
            对于 每个唯一的wild_id组:
                提取当前组的数据(图像, 问题, 答案, 权重)
                计算当前组损失 = 模型前向传播(当前组数据, 零样本模式=True)
                累加零样本损失

            平均零样本损失 = 零样本损失 / 组数

        总损失 = 常规损失 + 零样本损失
        反向传播并更新参数
        记录所有指标

    返回 训练统计信息

函数 evaluate(模型, 数据加载器, 设备, 配置, 智能体):
    设置模型为评估模式
    初始化指标记录器

    对于 数据加载器中的每个批次:
        获取模型预测答案 = 模型推理(图像, 问题, 生成模式)

        对于 批次中的每个样本:
            预处理预测答案(去除标点, 标准化数字和冠词)
            预处理真实答案

            如果 真实答案是单个字符串:
                准确率 = 1 如果预测==真实 否则 0
            否则:
                # VQA评估协议: 计算与其他答案的匹配度
                对于 每个真实答案:
                    计算匹配数 = 预测答案在其他答案中的出现次数
                    当前准确率 = min(1, 匹配数/3)
                准确率 = 所有准确率的平均值

        更新批次准确率

    返回 评估统计信息

主函数 main(参数, 配置, 评估模式=False, 测试EMA=False):

    # ===== 初始化阶段 =====
    获取智能体对象
    设置结果保存目录
    获取计算设备

    # ===== 数据准备阶段 =====
    创建数据集(训练集, 验证集, 测试集)

    如果 分布式训练:
        创建分布式数据采样器
    否则:
        采样器 = None

    设置批次大小 = [训练批次, 验证批次, 测试批次]
    创建数据加载器(训练, 验证, 测试)

    # ===== 模型准备阶段 =====
    创建BLIP_VQA模型(预训练路径, 图像尺寸, ViT配置, 智能体)
    移动模型到设备

    如果 分布式训练:
        包装模型为分布式并行模型

    # ===== 参数配置阶段 =====
    如果 启用EMA且非评估模式:
        根据EMA类型初始化LoRA参数

    如果 冻结编码器模式:
        如果 使用LoRA:
            添加LoRA参数到优化器
            标记只有LoRA参数可训练

        如果 第一个任务:
            启用分类头训练
        否则:
            锁定分类头

        创建优化器(仅指定参数)
    否则:
        创建优化器(所有参数)

    计算并打印可训练参数数量
    初始化智能体

    # ===== 特殊模式处理 =====
    如果 测试EMA模式:
        设置智能体为EMA融合模式
        重新创建模型
        执行评估
        返回结果

    如果 不是第一个任务且启用ZAF:
        创建零样本学习数据集和数据加载器

    # ===== 训练主循环 =====
    初始化最佳性能记录
    从检查点恢复训练状态(如果存在)

    对于 轮次 从 起始轮次 到 最大轮次:

        如果 非评估模式:
            如果 分布式训练:
                设置采样器轮次种子

            应用余弦学习率调度

            如果 需要零样本学习训练:
                执行 train_zsl(...)
            否则:
                执行 train(...)

            如果 启用EMA且达到更新频率:
                更新各模块的EMA参数
                执行验证评估
                根据保存策略保存模型

        如果 达到评估条件:
            执行验证评估
            执行测试评估

            如果 主进程:
                如果 评估模式:
                    记录验证和测试结果
                否则:
                    记录训练、验证、测试结果
                    更新最佳性能记录
                    保存模型(根据策略)
                    保存检查点
                    删除旧检查点
                    打印训练进度

        同步所有进程
        清空GPU缓存

        如果 评估模式:
            返回测试准确率

程序结束

============================================================================
"""

# 基础库导入
import argparse  # 命令行参数解析
import os  # 操作系统接口
import copy  # 深拷贝功能
import ruamel.yaml as yaml  # YAML文件解析
import numpy as np  # 数值计算
import random  # 随机数生成
import time  # 时间相关功能
import datetime  # 日期时间处理
import json  # JSON数据处理
from pathlib import Path  # 路径操作
import pickle  # 序列化

# PyTorch深度学习框架相关导入
import torch  # PyTorch核心
import torch.nn as nn  # 神经网络模块
import torch.nn.functional as F  # 神经网络函数
from torch.utils.data import DataLoader  # 数据加载器
import torch.backends.cudnn as cudnn  # CUDA深度神经网络库
import torch.distributed as dist  # 分布式训练
from itertools import zip_longest  # 迭代器工具

# 自定义数据处理模块
from data.vqa_dataset import vqa_collate_fn, vqa_collate_fn_zsl  # VQA数据集批处理函数
from models.blip_vqa import blip_vqa  # BLIP VQA模型

# 工具函数导入
import utils
from utils import (
    cosine_lr_schedule,
    warmup_lr_schedule,
    count_parameters,
)  # 学习率调度和参数计数
from data import (
    create_dataset,
    create_sampler,
    create_loader,
    create_zsl_dataset,
)  # 数据创建函数
from data.vqa_eval import VQAEval  # VQA评估工具
import loralib as lora  # LoRA (Low-Rank Adaptation) 库

# 初始化VQA评估工具
vqa_tool = VQAEval()


def train(model, data_loader, optimizer, epoch, device, config, agent):
    """
    VQA模型训练函数

    参数:
        model: 待训练的VQA模型
        data_loader: 训练数据加载器
        optimizer: 优化器
        epoch: 当前训练轮次
        device: 计算设备 (CPU/GPU)
        config: 配置字典
        agent: 智能体对象，包含训练策略和参数

    返回:
        dict: 包含训练统计信息的字典
    """
    # 设置模型为训练模式
    model.train()

    # 初始化指标记录器，用于记录训练过程中的各种指标
    metric_logger = utils.MetricLogger(delimiter="  ")
    # 添加学习率记录器，窗口大小为50，格式为6位小数
    metric_logger.add_meter(
        "lr", utils.SmoothedValue(window_size=50, fmt="{value:.6f}")
    )
    # 添加损失记录器，窗口大小为50，格式为4位小数
    metric_logger.add_meter(
        "loss", utils.SmoothedValue(window_size=50, fmt="{value:.4f}")
    )

    # 设置训练日志头部信息
    header = "Train Epoch: [{}]".format(epoch)
    # 设置打印频率，每50个batch打印一次
    print_freq = 50

    # 遍历训练数据，每个batch包含图像、问题、答案、权重和数量
    for i, (image, question, answer, weights, n) in enumerate(
        metric_logger.log_every(data_loader, print_freq, header)
    ):
        # 将图像和权重数据移动到指定设备 (GPU/CPU)
        image, weights = image.to(device), weights.to(device)

        # 前向传播计算损失
        # train=True表示训练模式，n为样本数量，weights为样本权重，agent为智能体
        loss = model(
            image, question, answer, train=True, n=n, weights=weights, agent=agent
        )

        # 反向传播和参数更新
        optimizer.zero_grad()  # 清零梯度
        loss.backward()  # 反向传播计算梯度
        optimizer.step()  # 更新模型参数

        # 更新指标记录器
        metric_logger.update(lr=optimizer.param_groups[0]["lr"])  # 记录当前学习率
        metric_logger.update(loss=loss.item())  # 记录当前损失

    # 同步不同进程间的统计信息 (用于分布式训练)
    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger.global_avg())

    # 返回训练统计信息字典
    return {
        k: "{:.4f}".format(meter.global_avg)
        for k, meter in metric_logger.meters.items()
    }


# to modify
def train_zsl(
    model,
    data_loader,
    zsl_data_loader,
    zsl_datasets,
    samplers,
    num_workers,
    optimizer,
    epoch,
    device,
    config,
    agent,
):
    """
    零样本学习 (Zero-Shot Learning) 训练函数
    结合常规训练数据和零样本数据进行联合训练

    参数:
        model: 待训练的VQA模型
        data_loader: 常规训练数据加载器
        zsl_data_loader: 零样本学习数据加载器
        zsl_datasets: 零样本数据集
        samplers: 数据采样器
        num_workers: 数据加载的工作进程数
        optimizer: 优化器
        epoch: 当前训练轮次
        device: 计算设备 (CPU/GPU)
        config: 配置字典
        agent: 智能体对象，包含训练策略和参数

    返回:
        dict: 包含训练统计信息的字典
    """
    # 设置模型为训练模式
    model.train()

    # 初始化指标记录器
    metric_logger = utils.MetricLogger(delimiter="  ")
    # 学习率记录器
    metric_logger.add_meter(
        "lr", utils.SmoothedValue(window_size=50, fmt="{value:.6f}")
    )
    # 总损失记录器
    metric_logger.add_meter(
        "loss", utils.SmoothedValue(window_size=50, fmt="{value:.4f}")
    )
    # 零样本损失记录器
    metric_logger.add_meter(
        "zero_shot_loss", utils.SmoothedValue(window_size=50, fmt="{value:.4f}")
    )

    header = "Train Epoch: [{}]".format(epoch)
    print_freq = 50
    step_size = 10

    # 同时遍历常规数据和零样本数据，使用zip_longest确保两个数据加载器都能完整遍历
    for i, (batch_data, zsl_batch_data) in enumerate(
        zip_longest(data_loader, zsl_data_loader, fillvalue=(None, None, None, None))
    ):
        # 检查批次数据是否为空，如果为空则跳出循环
        if all(item is None for item in batch_data):
            break
        if all(item is None for item in zsl_batch_data):
            break

        # 处理常规训练数据
        image, question, answer, weights, n = batch_data
        image, weights = image.to(device), weights.to(device)

        # 计算常规训练损失
        loss1 = model(
            image, question, answer, train=True, n=n, weights=weights, agent=agent
        )

        # 检查是否需要进行特定类型的零样本学习训练
        if (
            agent.train_distill_type == "zsl-single"
            or agent.train_distill_type == "ema-zsl-single"
            or agent.train_distill_type == "adv_text_zsl"
            or agent.train_distill_type == "multi-zsl-single"
        ):
            # 处理零样本学习数据
            image, question, answer, weights, n, wild_id = zsl_batch_data
            image, weights = image.to(device), weights.to(device)
            # 初始化零样本损失
            loss_zsl = torch.zeros(1, dtype=torch.float32, device=device)

            # 将wild_id转换为tensor并获取唯一值
            wild_id_tensor = torch.tensor(wild_id, dtype=torch.int64, device=device)
            unique_wild_ids = torch.unique(wild_id_tensor)  # 获取所有 unique 的 wild_id
            num_batches = 0

            # 遍历每个unique的wild_id，分别计算损失
            for current_wild_id in unique_wild_ids:  # 遍历每个 unique wild_id
                # 找到当前wild_id对应的数据索引
                current_indices = torch.where(wild_id_tensor == current_wild_id)[0]

                # 提取当前wild_id对应的数据
                current_images = image[current_indices]
                current_weights = weights[current_indices]
                current_id = wild_id[current_indices]

                # 将索引转换为列表形式，用于提取对应的问题和答案
                current_indices_list = current_indices.tolist()
                current_question = [question[i] for i in current_indices_list]
                current_answer = [answer[i] for i in current_indices_list]
                current_n = [n[i] for i in current_indices_list]
                current_id = current_id.tolist()

                # 调试信息打印
                print(current_question)
                print(current_answer)
                print(current_n)
                print(current_weights)
                print(current_id)

                # 计算当前wild_id组的零样本损失
                loss = model(
                    current_images,
                    current_question,
                    current_answer,
                    train=True,
                    n=current_n,
                    weights=current_weights,
                    train_zsl=True,  # 标记为零样本训练
                    agent=agent,
                    wild_id=current_id,
                )

                # 累加零样本损失
                loss_zsl = loss_zsl + loss
                num_batches += 1

        # 计算平均零样本损失
        loss2 = loss_zsl / num_batches
        # 总损失 = 常规损失 + 零样本损失
        loss = loss1 + loss2

        # 反向传播和参数更新
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 更新指标记录器
        metric_logger.update(lr=optimizer.param_groups[0]["lr"])
        metric_logger.update(loss=loss.item())
        metric_logger.update(zero_shot_loss=loss2.item())

        # 定期打印训练进度
        if i % print_freq == 0 and i != 0:
            print(
                f"Step: {i}, Loss: {loss.item():.4f}, ce-Loss: {loss1.item():.4f}, zero-shot-Loss: {loss2.item():.4f}"
            )

    # 同步进程间的统计信息
    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger.global_avg())

    # 返回训练统计信息
    return {
        k: "{:.4f}".format(meter.global_avg)
        for k, meter in metric_logger.meters.items()
    }


@torch.no_grad()
def evaluate(model, data_loader, device, config, agent):
    """
    VQA模型评估函数
    使用VQA评估协议计算模型在验证/测试集上的准确率

    参数:
        model: 待评估的VQA模型
        data_loader: 评估数据加载器
        device: 计算设备 (CPU/GPU)
        config: 配置字典
        agent: 智能体对象

    返回:
        dict: 包含评估统计信息的字典，主要是准确率
    """
    # 设置模型为评估模式，禁用dropout和batch normalization的训练行为
    model.eval()

    # 初始化指标记录器
    metric_logger = utils.MetricLogger(delimiter="  ")

    header = "Evaluation:"
    print_freq = 50

    # 遍历评估数据，torch.no_grad()确保不计算梯度以节省内存
    for i, (image, question, answer, weights, n) in enumerate(
        metric_logger.log_every(data_loader, print_freq, header)
    ):
        # 将图像数据移动到指定设备
        image = image.to(device)
        # 保存真实答案用于后续准确率计算
        gt_answer = answer

        # 如果配置为生成式推理模式
        if config["inference"] == "generate":
            # 获取模型预测答案
            predicted_answers = model(
                image, question, train=False, inference="generate"
            )

            # 初始化保存准确率的列表
            accQA = []

            # 遍历当前 batch 中的每个样本
            for batch_idx in range(image.size(0)):
                # 获取当前样本的预测答案
                resAns = predicted_answers[batch_idx]

                # 如果预测答案是列表，取第一个元素
                if isinstance(resAns, list):
                    resAns = resAns[0]

                # 处理预测答案：去除换行符、制表符，并处理标点符号、数字和冠词
                resAns = resAns.replace("\n", " ").replace("\t", " ").strip()
                resAns = vqa_tool.processPunctuation(resAns)  # 标点符号标准化
                resAns = vqa_tool.processDigitArticle(resAns)  # 数字和冠词标准化

                # 处理真实答案
                # 检查是否每个样本都有对应的真实答案
                if len(gt_answer) == image.size(0):
                    # 简单情况：一对一对应
                    gtAnswers = gt_answer[batch_idx]
                else:
                    # 复杂情况：需要根据样本数量n来确定对应的答案范围
                    gtAnswers = []
                    gtAcc = []

                    # 计算当前样本对应的答案起始索引
                    start = 0
                    for i in n[:batch_idx]:
                        start += i

                    # 计算当前样本对应的答案结束索引
                    end = 0
                    for i in n[: batch_idx + 1]:
                        end += i

                    # 提取当前样本的所有真实答案并进行标点符号处理
                    for ansDic in gt_answer[start:end]:
                        gtAnswers.append(vqa_tool.processPunctuation(ansDic))

                    # 获取对应的权重
                    gt_weight = weights[start:end]

                    # 根据权重来还原到原来的10个答案（VQA数据集标准）
                    new_answers = []
                    for answer, weight in zip(gtAnswers, gt_weight):
                        # 根据权重计算复制次数，权重*10得到该答案在10个标注中出现的次数
                        num_copies = int(weight * 10)
                        # 复制相应次数的答案
                        new_answers.extend([answer] * num_copies)

                    gtAnswers = new_answers

                # 计算准确率
                if isinstance(gtAnswers, str):
                    # 单个真实答案的情况
                    gtAnswer = gtAnswers
                    gtAnswer = vqa_tool.processPunctuation(gtAnswer)

                    # 完全匹配则准确率为1.0，否则为0.0
                    if resAns == gtAnswer:
                        accQA.append(1.0)
                    else:
                        accQA.append(0.0)
                else:
                    # 多个真实答案的情况，使用VQA评估协议
                    # 对每个真实答案计算匹配度
                    for gtAnsDatum in gtAnswers:
                        # 创建其他答案的副本
                        otherGTAns = copy.deepcopy(gtAnswers)
                        otherGTAns.remove(gtAnsDatum)

                        # 计算预测答案与其他真实答案的匹配数量
                        matchingAns = [item for item in otherGTAns if item == resAns]

                        # VQA准确率计算公式：min(1, 匹配数量/3)
                        # 这是VQA数据集的标准评估方式
                        acc = min(1, float(len(matchingAns)) / 3)
                        gtAcc.append(acc)

                    # 计算当前样本的平均准确率
                    if gtAcc:
                        avgGTAcc = float(sum(gtAcc)) / len(gtAcc)
                        accQA.append(avgGTAcc)
                    else:
                        accQA.append(0)

            # 计算当前 batch 的平均准确率
            batch_accuracy = sum(accQA) / len(accQA)

            # 更新指标记录器中的准确率
            metric_logger.meters["acc"].update(batch_accuracy, n=image.size(0))

    # 同步不同进程间的统计信息 (用于分布式评估)
    metric_logger.synchronize_between_processes()

    print("Averaged stats:", metric_logger.global_avg())

    # 返回评估统计信息字典
    return {
        k: "{:.4f}".format(meter.global_avg)
        for k, meter in metric_logger.meters.items()
    }


def main(args, config, eval=False, test_ema=False):
    """
    主训练/评估函数

    参数:
        args: 命令行参数字典
        config: 配置参数字典
        eval: 是否仅进行评估 (默认False)
        test_ema: 是否测试EMA模型 (默认False)

    返回:
        测试准确率 (评估模式下) 或 None (训练模式下)
    """

    # 获取智能体对象，包含训练策略和参数
    agent = args["agent"]

    # 设置结果保存目录
    # change all "args." to args[""]
    args["result_dir"] = os.path.join(args["out_dir"], "result")
    # 如果是主进程，创建结果目录
    if utils.is_main_process():
        Path(args["result_dir"]).mkdir(parents=True, exist_ok=True)
    # 获取计算设备
    device = args["device"]

    #### Dataset ####
    print("Creating dataset")
    # 准备数据集参数字典
    dataset_pass_dict = {"training_data_sample": args["training_data_sample"]}
    # 创建训练、验证、测试数据集
    datasets = create_dataset(config["dataset"], config, dataset_pass_dict)

    # 处理分布式训练的数据采样
    if args["distributed"]:
        # 分布式训练：获取进程数和当前进程rank
        num_tasks = utils.get_world_size()
        global_rank = utils.get_rank()
        # 创建分布式数据采样器：[训练集需要采样, 验证集不需要, 测试集不需要]
        samplers = create_sampler(
            datasets, [True, False, False], num_tasks, global_rank
        )
    else:
        # 单机训练：不需要特殊采样器
        samplers = [None, None, None]

    # 设置各个数据集的批次大小
    batch_size = [
        config["batch_size_train"][agent.task_id],  # 训练批次大小（根据任务ID）
        config["batch_size_test"],  # 验证批次大小
        config["batch_size_test"],  # 测试批次大小
    ]

    # 创建数据加载器
    train_loader, val_loader, test_loader = create_loader(
        datasets,
        samplers,
        batch_size=batch_size,
        num_workers=[
            args["num_workers"],
            args["num_workers"],
            args["num_workers"],
        ],  # 数据加载工作进程数
        is_trains=[True, False, False],  # 训练模式标志：[训练, 验证, 测试]
        collate_fns=[vqa_collate_fn, vqa_collate_fn, vqa_collate_fn],  # 批处理函数
    )

    # 重新获取智能体对象（可能是为了确保一致性）
    agent = args["agent"]

    #### Model ####
    print("Creating model")
    # 创建BLIP VQA模型
    model, head_not_loaded = blip_vqa(
        pretrained=args["pretrained"],  # 预训练模型路径
        image_size=config["image_size"],  # 输入图像尺寸
        vit=config["vit"],  # Vision Transformer配置
        vit_grad_ckpt=config["vit_grad_ckpt"],  # ViT梯度检查点
        vit_ckpt_layer=config["vit_ckpt_layer"],  # ViT检查点层数
        agent=agent,  # 智能体对象
    )

    # 将模型移动到指定设备
    model = model.to(device)

    # 保存模型的原始引用（用于分布式训练）
    model_without_ddp = model
    if args["distributed"]:
        # 分布式训练：使用DistributedDataParallel包装模型
        model = torch.nn.parallel.DistributedDataParallel(
            model, device_ids=[args["gpu"]], find_unused_parameters=True
        )
        # 保持对原始模型的引用
        model_without_ddp = model.module

    # EMA (Exponential Moving Average) 初始化
    if not eval and agent.ema:
        if args["ema_lora"] == "continual":
            print("continual training lora ")
            pass
        elif args["ema_lora"] == "zero" or agent.train_distill_type == "grassmann":
            print("initial lora with defination")
            # 使用默认定义初始化LoRA
            lora.lora_initial(model_without_ddp.text_encoder)
            lora.lora_initial(model_without_ddp.visual_encoder)
        elif args["ema_lora"] == "ema":
            print("initial lora with ema")
            # 使用EMA方式初始化LoRA
            lora.lora_initial_ema(model_without_ddp.text_encoder)
            lora.lora_initial_ema(model_without_ddp.visual_encoder)
            lora.lora_initial_ema(model_without_ddp.text_encoder.bert)

    # 配置可训练参数
    if agent.freeze_encoders:
        # 冻结编码器模式：只训练特定部分

        param_to_optim = []

        if agent.lora:
            # 添加LoRA相关参数到优化器
            param_to_optim += list(model_without_ddp.text_encoder.parameters())
            param_to_optim += list(model_without_ddp.visual_encoder.parameters())
            param_to_optim += list(model_without_ddp.text_decoder.parameters())

            # 标记只有LoRA参数可训练
            lora.mark_only_lora_as_trainable(model_without_ddp.text_encoder)
            lora.mark_only_lora_as_trainable(model_without_ddp.visual_encoder)
            lora.mark_only_lora_as_trainable(model_without_ddp.text_decoder)

        # 任务头部参数配置
        if agent.task_id == 0:
            print("Training head")
            # 第一个任务：训练分类头
            for p in model_without_ddp.text_decoder.cls.parameters():
                p.requires_grad = True
        else:
            print("Locking head")
            # 后续任务：锁定分类头
            for p in model_without_ddp.text_decoder.cls.parameters():
                p.requires_grad = False

        # 创建优化器（仅优化指定参数）
        optimizer = torch.optim.AdamW(
            params=param_to_optim,
            lr=config["init_lr"],  # 初始学习率
            weight_decay=config["weight_decay"],  # 权重衰减
        )
        # 计算可训练参数数量
        nparam = count_parameters(param_to_optim)
    else:
        # 全模型训练模式
        optimizer = torch.optim.AdamW(
            params=model.parameters(),
            lr=config["init_lr"],
            weight_decay=config["weight_decay"],
        )
        # 计算可训练参数数量
        nparam = count_parameters(model.parameters())

    # 打印可训练参数数量
    print(f"trainable_parameters = {nparam}")

    # 初始化智能体（非评估模式下）
    if not eval:
        agent.update_model(model_without_ddp)

    print("Start training")
    start_time = time.time()
    # 初始化最佳性能记录
    best = 0  # 最佳验证准确率
    best_epoch = 0  # 最佳性能对应的轮次

    # 特殊情况：如果eval_every < 0，则不进行训练，直接保存初始模型
    if not eval and args["eval_every"] < 0:
        if utils.is_main_process():
            torch.save(
                {"model": model_without_ddp.state_dict()}, args["model_save_path"]
            )
        return

    # 设置训练起始轮次
    start_epoch = 0

    # 从检查点恢复训练状态
    # load checkpint of current task
    for epoch in range(start_epoch, config["max_epoch"]):
        # 构造检查点文件路径
        load_file = os.path.join(args["out_dir"], "checkpoint_%02d.pth" % epoch)
        if os.path.exists(load_file):
            # 加载检查点
            checkpoint = torch.load(load_file)
            model_without_ddp.load_state_dict(checkpoint["model"])  # 加载模型参数
            optimizer.load_state_dict(checkpoint["optimizer"])  # 加载优化器状态
            start_epoch = checkpoint["epoch"] + 1  # 设置起始轮次
            best = checkpoint["best"]  # 恢复最佳性能
            best_epoch = checkpoint["best_epoch"]  # 恢复最佳轮次

    if test_ema:
        # EMA模型测试模式
        # 保存原始融合类型
        fuse_type = agent.fuse_type
        print("evaluate ema")

        # 重新获取智能体并设置为EMA融合模式
        agent = args["agent"]
        agent.fuse_type = "ema"

        print("Creating model")
        # 创建EMA模型用于测试
        model, head_not_loaded = blip_vqa(
            pretrained=args["pretrained"],
            image_size=config["image_size"],
            vit=config["vit"],
            vit_grad_ckpt=config["vit_grad_ckpt"],
            vit_ckpt_layer=config["vit_ckpt_layer"],
            agent=agent,
        )

        model = model.to(device)
        # 分布式训练包装
        if args["distributed"]:
            model = torch.nn.parallel.DistributedDataParallel(
                model, device_ids=[args["gpu"]], find_unused_parameters=True
            )

        # 使用评估函数测试EMA模型
        eval_func = evaluate
        test_stats = eval_func(model, test_loader, device, config, agent)

        # 恢复原始融合类型
        agent.fuse_type = fuse_type

        # 返回测试结果
        if utils.is_main_process():
            return test_stats["acc"]
        else:
            return -0.1

    else:
        # 正常训练模式

        # 如果不是第一个任务且启用了ZAF (Zero-shot Adaptation Framework)
        if agent.task_id != 0 and agent.zaf == True:
            print("Creating zsl dataset")
            # 创建零样本学习数据集
            dataset_pass_dict = {"training_data_sample": args["training_data_sample"]}
            zsl_datasets = create_zsl_dataset(
                config["dataset"], config, dataset_pass_dict
            )

            # 分布式训练的采样器设置
            if args["distributed"]:
                num_tasks = utils.get_world_size()
                global_rank = utils.get_rank()
                samplers = create_sampler(
                    zsl_datasets, [True, False, False], num_tasks, global_rank
                )
            else:
                samplers = [None, None, None]

            # 批次大小设置
            batch_size = [
                config["batch_size_train"][agent.task_id],
                config["batch_size_test"],
                config["batch_size_test"],
            ]

            # 创建零样本学习数据加载器
            zsl_train_loader, _, _ = create_loader(
                zsl_datasets,
                samplers,
                batch_size=batch_size,
                num_workers=[
                    args["num_workers"],
                    args["num_workers"],
                    args["num_workers"],
                ],
                is_trains=[True, False, False],
                collate_fns=[
                    vqa_collate_fn_zsl,
                    vqa_collate_fn_zsl,
                    vqa_collate_fn_zsl,
                ],
            )

        # 重置最佳性能记录
        best = 0
        for epoch in range(start_epoch, config["max_epoch"]):
            # 主训练循环：从起始轮次到最大轮次

            if not eval:
                # 训练模式（非评估模式）

                if args["distributed"]:
                    # 分布式训练：为每个epoch设置随机种子
                    train_loader.sampler.set_epoch(epoch)

                # 应用余弦学习率调度
                cosine_lr_schedule(
                    optimizer,
                    epoch,
                    config["max_epoch"],  # 最大训练轮次
                    config["init_lr"],  # 初始学习率
                    config["min_lr"],  # 最小学习率
                )

                # 选择训练函数：零样本学习训练 vs 常规训练
                if agent.task_id != 0 and agent.zaf == True:
                    # 零样本学习训练模式
                    train_stats = train_zsl(
                        model,
                        train_loader,
                        zsl_train_loader,
                        zsl_datasets,
                        samplers,
                        args["num_workers"],
                        optimizer,
                        epoch,
                        device,
                        config,
                        agent,
                    )
                else:
                    # 常规训练模式
                    train_stats = train(
                        model, train_loader, optimizer, epoch, device, config, agent
                    )

                # EMA (指数移动平均) 更新
                if agent.ema and (epoch + 1) % args["ema_frequency"] == 0:
                    frequency = args["ema_frequency"]
                    if args["ema"] == "epoch":
                        # 按轮次进行EMA更新
                        ema_alpha = args["ema_alpha"]
                        print(
                            f"epoch EMA begins,current_alpha = {ema_alpha},task_id = {agent.task_id},ema_frequency = {frequency}"
                        )

                        # 更新各个模块的EMA参数
                        lora.update_ema_epoch_lora(
                            model_without_ddp.text_encoder,
                            args["ema_alpha"],  # EMA衰减系数
                            agent.task_id,  # 当前任务ID
                            agent.update_both,  # 是否同时更新两个参数
                        )
                        lora.update_ema_epoch_lora(
                            model_without_ddp.visual_encoder,
                            args["ema_alpha"],
                            agent.task_id,
                            agent.update_both,
                        )
                        lora.update_ema_epoch_lora(
                            model_without_ddp.text_decoder.bert,
                            args["ema_alpha"],
                            agent.task_id,
                            agent.update_both,
                        )

                        # EMA更新后立即进行验证
                        val_stats = evaluate(model, val_loader, device, config, agent)

                        # 模型保存策略
                        if args["save_frequency"] == "best":
                            # 只保存最佳模型
                            if float(val_stats["acc"]) > best:
                                best = float(val_stats["acc"])
                                torch.save(
                                    {"model": model_without_ddp.state_dict()},
                                    args["model_save_path"],
                                )
                        elif args["save_frequency"] == "every":
                            # 每次EMA更新都保存
                            torch.save(
                                {"model": model_without_ddp.state_dict()},
                                args["model_save_path"],
                            )
                    else:
                        pass

            if eval or (epoch + 1) % args["eval_every"] == 0:
                # 评估条件：评估模式 或 达到评估频率

                # 使用评估函数
                eval_func = evaluate

                # 在验证集和测试集上进行评估
                val_stats = eval_func(model, val_loader, device, config, agent)
                test_stats = eval_func(model, test_loader, device, config, agent)

                if utils.is_main_process():
                    # 主进程负责日志记录和模型保存

                    if eval:
                        # 纯评估模式：只记录验证和测试结果
                        log_stats = {
                            **{f"val_{k}": v for k, v in val_stats.items()},
                            **{f"test_{k}": v for k, v in test_stats.items()},
                        }
                        # 写入日志文件
                        with open(os.path.join(args["out_dir"], "log.txt"), "a") as f:
                            f.write(json.dumps(log_stats) + "\n")

                    else:
                        # 训练模式：记录训练、验证和测试结果
                        log_stats = {
                            **{f"train_{k}": v for k, v in train_stats.items()},
                            **{f"val_{k}": v for k, v in val_stats.items()},
                            **{f"test_{k}": v for k, v in test_stats.items()},
                            "epoch": epoch,
                        }

                        # 更新最佳性能记录
                        if float(val_stats["acc"]) > best:
                            best = float(val_stats["acc"])
                            best_epoch = epoch
                            # 非EMA模式下保存最佳模型
                            if not agent.ema:
                                if args["save_frequency"] == "best":
                                    torch.save(
                                        {"model": model_without_ddp.state_dict()},
                                        args["model_save_path"],
                                    )

                        # 非EMA模式下的每轮保存
                        if not agent.ema and args["save_frequency"] == "every":
                            torch.save(
                                {"model": model_without_ddp.state_dict()},
                                args["model_save_path"],
                            )

                        # 写入训练日志
                        with open(os.path.join(args["out_dir"], "log.txt"), "a") as f:
                            f.write(json.dumps(log_stats) + "\n")

                        # 保存检查点
                        save_obj = {
                            "model": model_without_ddp.state_dict(),  # 模型参数
                            "optimizer": optimizer.state_dict(),  # 优化器状态
                            "config": config,  # 配置信息
                            "epoch": epoch,  # 当前轮次
                            "best": best,  # 最佳性能
                            "best_epoch": best_epoch,  # 最佳轮次
                        }
                        torch.save(
                            save_obj,
                            os.path.join(
                                args["out_dir"], "checkpoint_%02d.pth" % epoch
                            ),
                        )

                        # 删除上一轮的检查点以节省空间
                        epoch_old = epoch - 1
                        old_file = os.path.join(
                            args["out_dir"], "checkpoint_%02d.pth" % epoch_old
                        )
                        if os.path.isfile(old_file):
                            os.remove(old_file)

                        # 打印当前训练进度
                        print(
                            f"Finished epoch {epoch} best epoch is {best_epoch} with acc {best}"
                        )

            # 同步所有进程（分布式训练）
            dist.barrier()
            # 清空GPU缓存
            torch.cuda.empty_cache()

            # 评估模式下提前返回结果
            if eval:
                if utils.is_main_process():
                    return test_stats["acc"]
                else:
                    return -0.1
