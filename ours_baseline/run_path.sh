
#export CUDA_VISIBLE_DEVICES=2,3,4,5,6,7
cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP


torchrun --nproc_per_node=1 --master_port=25331 run_me.py --config ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name LoRa --mu 16 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/3task_base_LoRA_0.00125/  --external_lr 0.00125


torchrun --nproc_per_node=1 --master_port=25332 run_me.py --config ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa --mu 16 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/3task_base_mutLoRA_0.00125/  --external_lr 0.00125

cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP
export CUDA_VISIBLE_DEVICES=2
torchrun --nproc_per_node=1 --master_port=25333 run_me.py --config ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name LoRa --mu 16 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/3task_base+cap_LoRA_0.00125/  --external_lr 0.00125

cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP
export CUDA_VISIBLE_DEVICES=3
torchrun --nproc_per_node=1 --master_port=25334 run_me.py --config ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa --mu 16 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/3task_base+cap_mutLoRA_0.00125/  --external_lr 0.00125


cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP
export CUDA_VISIBLE_DEVICES=4
torchrun --nproc_per_node=1 --master_port=25335 run_me.py --config ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name LoRa --mu 16 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/3task_base_vqa_LoRA_0.00125/  --external_lr 0.00125

cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP
export CUDA_VISIBLE_DEVICES=5
torchrun --nproc_per_node=1 --master_port=25336 run_me.py --config ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa --mu 16 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/3task_base_vqa_mutLoRA_0.00125/  --external_lr 0.00125



cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP
export CUDA_VISIBLE_DEVICES=6
torchrun --nproc_per_node=1 --master_port=25337 run_me.py --config  ./configs/pathvqa_task_order/what_judge_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/base_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/pathvqa_task_order/2zero_shot.yaml

torchrun --nproc_per_node=1 --master_port=25338 run_me.py --config  ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/base_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/pathvqa_task_order/3zero_shot.yaml



cd /root/xingxing/project/nvlr_vqa_multi_wild_path/
conda activate BLIP
export CUDA_VISIBLE_DEVICES=7
torchrun --nproc_per_node=1 --master_port=25339 run_me.py --config  ./configs/pathvqa_task_order/what_judge_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/base+cap_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/pathvqa_task_order/2zero_shot.yaml

torchrun --nproc_per_node=1 --master_port=25340 run_me.py --config  ./configs/pathvqa_task_order/3task_pathvqa_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency  every --output_dir /root/xingxing/project/result_pathvqa/base+cap_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/pathvqa_task_order/3zero_shot.yaml
