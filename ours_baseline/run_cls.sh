export CUDA_VISIBLE_DEVICES=4,5,6,7
cd /root/xingxing/NMI/ours/

torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/final_vaw/5task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/2zero_shot.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name CLS --mu 16 --external_lr 0.00125 --save_frequency every --ema_alpha 0.85 --output_dir /root/xingxing/project/result_nvlr/VAW-CLS-ER