import torch
import torch.nn as nn
from torch.nn import functional as F
import copy
import numpy as np
from pathlib import Path
import shutil
import os
import utils
import torch.distributed as dist
from agents.base import Base

#####################
# Base LoRa class #
#####################

#lora
class LoRa(Base):
    def __init__(self, agent_config):
        super(LoRa, self).__init__(agent_config)
        self.lora = True
        self.r = int(self.mu)
        # initialize prompts
        self.freeze_encoders = True
        self.freeze_heads = True
        self.layer_keys = [0]

#layred-lora
class MultiLoRa(LoRa):
    def __init__(self, agent_config):
        super(MultiLoRa, self).__init__(agent_config)
        self.freeze_heads = True
        self.multi = True
        # 推理时看前面所有lora的结果
        self.fuse_type = 'last'

#ConStruct-VL: Data-Free Continual Structured VL Concepts Learning
class AdvTextMultiLoRa(MultiLoRa):
    def __init__(self, agent_config):
        super(AdvTextMultiLoRa, self).__init__(agent_config)
        self.train_distill_type = 'adv_text'
        #推理时看前面所有lora的结果
        self.fuse_type = 'last'

#EMA
class EMALoRa(LoRa):
    def __init__(self, agent_config):
        super(EMALoRa, self).__init__(agent_config)
        self.freeze_heads = True
        self.ema = True

class LwF(LoRa):
    def __init__(self, agent_config):
        super(LwF, self).__init__(agent_config)
        self.freeze_heads = True
        self.LwF = True

class ER(LoRa):
    def __init__(self, agent_config):
        super(ER, self).__init__(agent_config)
        self.freeze_heads = True
        self.memory_size = 20


class EWC(LoRa):
    def __init__(self, agent_config):
        super(EWC, self).__init__(agent_config)
        self.freeze_heads = True
        self.ewc = True
        self.fisher = None
        self.mean = None

class GEM(LoRa):
    def __init__(self, agent_config):
        super(GEM, self).__init__(agent_config)
        self.freeze_heads = True
        self.gem = True
        self.memory_size = 20

#CLS
class CLS(LoRa):
    def __init__(self, agent_config):
        super(CLS, self).__init__(agent_config)
        self.freeze_heads = True
        self.cls = True
        self.memory_size = 20

#ZAF
class ZAF(LoRa):
    def __init__(self, agent_config):
        super(ZAF, self).__init__(agent_config)
        self.freeze_heads = True
        self.ema = True
        self.train_distill_type = 'ema-zsl-single'
        #the random setting shuffles the texts and makes them unpaired.
        self.random = True
        self.zaf = True

class MultiLoRa_wild(LoRa):
    def __init__(self, agent_config):
        super(MultiLoRa_wild, self).__init__(agent_config)
        self.freeze_heads = True
        self.ema = False
        self.multi = True
        self.train_distill_type = 'multi-zsl-single'
        self.random = False
        self.zaf = True
        # self.wild_id = None
