"""
零样本学习评估脚本 - 详细伪代码

=== 主要功能概述 ===
本脚本实现了一个专门用于零样本学习评估的持续学习框架，
在训练过程中同时评估模型在所有任务上的零样本性能

=== 详细伪代码流程 ===

1. 初始化阶段:
   输入: 配置文件, 命令行参数
   BEGIN 初始化
       解析命令行参数 (任务配置, 输出目录, 代理类型等)
       设置随机种子确保可重复性
       初始化分布式训练环境
       加载任务序列配置
       创建零样本学习代理(agent)

       IF 存在Oracle上界结果文件 THEN
           加载Oracle结果作为归一化基准
       IF 存在Lower Bound下界结果文件 THEN
           加载LB结果作为归一化基准

       初始化结果字典:
       - cl_matrix[task_id] = []  // 持续学习性能矩阵
       - acc_cur = []             // 当前步骤平均准确率
       - forg_cur = []            // 当前步骤遗忘率
       - zero_shot_acc = []       // 零样本准确率矩阵
   END 初始化

2. 原始零样本评估(可选):
   IF 原始零样本结果不存在 THEN
       BEGIN 原始零样本评估
           FOR each_task t in 所有任务:
               使用未训练模型评估任务t
               记录零样本性能到 zero_shot_acc[0][t]
           保存原始零样本结果
       END 原始零样本评估

3. 主训练循环:
   FOR each_task t in 任务序列:
       BEGIN 任务处理
           A. 任务准备阶段:
              加载第t个任务配置文件
              IF 指定外部学习率 THEN 覆盖配置中的学习率
              agent.increment_task(task_name, task_config)

           B. 训练阶段:
              准备训练参数字典
              IF 训练未完成 AND 非LB模式 THEN
                  调用 task_trainers[trainer_name].main(训练模式)
                  记录训练时间并保存完成标志

           C. 记忆重放阶段:
              IF memory > 0 THEN
                  扩展 agent.coreset 采样记忆样本

           D. 双重评估阶段:
              IF 评估结果不存在 THEN
                  D1. 持续学习评估:
                      调用 evaluate_tasks() 评估已见任务性能
                      更新 cl_matrix, acc_cur, forg_cur

                  D2. 零样本评估:
                      调用 evaluate_zeroshot() 评估所有任务零样本性能
                      更新 zero_shot_acc[t+1][all_tasks]

                  保存所有评估结果到文件系统
              ELSE
                  从已有文件加载评估结果

           E. 任务完成:
              agent.finish_task()
       END 任务处理

4. 持续学习任务评估子流程 (evaluate_tasks):
   输入: agent, 结果字典, 评估参数, 任务列表等

   IF agent是Oracle模式 THEN
       // Oracle只评估当前任务
       BEGIN Oracle评估
           准备当前任务评估目录 '_eval-only_X'
           设置模型检查点路径
           调用对应trainer进行评估
           添加结果到 cl_matrix[current_task]
       END Oracle评估
   ELSE
       // 普通模式评估所有已见任务
       BEGIN 全任务评估
           FOR each_seen_task i from 0 to current_task:
               准备任务i的评估目录 '_eval-only_ourX'
               调用对应trainer评估任务i
               添加结果到 cl_matrix[i]

           计算性能指标:
           - 当前步骤平均准确率: acc_cur = sum(当前性能) / 已见任务数
           - 当前步骤遗忘率: forg_cur = sum(最佳性能 - 当前性能) / 任务数
           - 平均遗忘率(复杂计算): 考虑时间维度的遗忘

           IF 是最后一个任务 THEN
               计算最终平均准确率和遗忘率
       END 全任务评估

5. 零样本评估子流程 (evaluate_zeroshot):
   输入: agent, 结果字典, 评估参数, 任务列表

   BEGIN 零样本评估
       FOR each_task t in 所有任务(包括未见过的):
           准备任务t的评估目录 '_eval-only_zeroX'
           使用当前训练后的模型评估任务t(零样本)
           添加结果到 zero_shot_acc[current_step+1][t]
   END 零样本评估

6. 结果保存和分析:
   每个任务完成后保存(文本格式):
   - cl_matrix: 持续学习性能矩阵
   - acc_cur: 各步骤平均准确率
   - forg_cur: 各步骤遗忘率
   - zero_shot_acc: 零样本性能演化矩阵

=== 关键数据结构 ===
- agent: 零样本学习代理，管理模型状态和任务转换
- cl_matrix[i][j]: 任务i在第j个训练步骤后的性能
- zero_shot_acc[step][task]: 第step步训练后对任务task的零样本性能
- acc_cur[step]: 第step步的平均性能
- forg_cur[step]: 第step步的遗忘率

=== 零样本学习特色功能 ===
- 双重评估机制: 同时评估持续学习和零样本性能
- 全任务零样本测试: 每步都测试对所有任务的零样本能力
- 性能演化追踪: 记录零样本能力随训练步骤的变化
- 原始基准测试: 评估未经训练模型的零样本基线
- 文本格式输出: 便于分析的.txt格式结果

=== 实验输出 ===
最终在output_dir生成:
- 各任务训练模型检查点
- 持续学习评估结果(cl_matrix等)
- 零样本性能演化记录(zero_shot_acc)
- 性能统计指标(acc_cur, forg_cur)
- 原始零样本基准(task_origin目录)
"""

import os
import sys
import argparse
import numpy as np
import yaml
import random
import agents
import task_trainers
import utils
from pathlib import Path
import time
import datetime

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.backends.cudnn as cudnn
import torch.distributed as dist
from torch.utils.data import DataLoader

SEED = 0
REVALUATE_FLAG = False


# evaluate on all tasks seen
def evaluate_tasks(
    agent,
    result_dict,
    eval_args,
    task_list,
    oracle_exists,
    oracle_results,
    lb_exists,
    lb_results,
    eval_ema,
):
    """
    ┌─────────────────────────────────────────────────────────────────────────────┐
    │                      遗忘率计算方法对比图解                                    │
    ├─────────────────────────────────────────────────────────────────────────────┤
    │                                                                             │
    │  方法1: 平均遗忘率 (avg_forgetting) - 复杂的后向传播遗忘                      │
    │  ┌─────────────────────────────────────────────────────────────────────┐    │
    │  │  考虑相邻时间步的性能差异                                            │    │
    │  │                                                                     │    │
    │  │  cl_matrix结构 (每个任务从其开始训练的步骤记录):                     │    │
    │  │  cl_matrix[0] = [80%, 75%, 70%, 65%]  ← Task0从step0开始           │    │
    │  │  cl_matrix[1] =      [90%, 85%, 80%]       ← Task1从step1开始           │    │
    │  │  cl_matrix[2] =           [95%, 90%]            ← Task2从step2开始           │    │
    │  │                                                                     │    │
    │  │  计算相邻步骤的性能差异:                                             │    │
    │  │  Step1→2: Task0: (75%-70%)=5%, 平均=5%                            │    │
    │  │  Step2→3: Task0: (70%-65%)=5%, Task1: (90%-85%)=5%, 平均=5%       │    │
    │  │  Step3→4: Task0: +Task1: +Task2: (95%-90%)=5%, 平均=5%            │    │
    │  │  最终平均: (5% + 5% + 5%) / 3 = 5%                                 │    │
    │  └─────────────────────────────────────────────────────────────────────┘    │
    │                                                                             │
    │  方法2: 即时遗忘率 (forg_cur) - 简单的最佳vs当前对比                          │
    │  ┌─────────────────────────────────────────────────────────────────────┐    │
    │  │  只比较历史最佳与当前性能                                            │    │
    │  │                                                                     │    │
    │  │  Task0: 历史最佳 80% → 当前 65%, 遗忘 = 15%                        │    │
    │  │  Task1: 历史最佳 90% → 当前 80%, 遗忘 = 10%                        │    │
    │  │  Task2: 历史最佳 95% → 当前 90%, 遗忘 = 5%                         │    │
    │  │                                                                     │    │
    │  │  平均遗忘率 = (15% + 10% + 5%) / 3 = 10%                          │    │
    │  └─────────────────────────────────────────────────────────────────────┘    │
    │                                                                             │
    │  核心区别:                                                                  │
    │  • 方法1: 关注渐进式遗忘过程，衡量学习过程中的连续退化                       │
    │  • 方法2: 关注总体遗忘结果，衡量从最佳状态到当前的总退化                     │
    │  • 方法1: 更适合分析学习动态过程                                            │
    │  • 方法2: 更适合评估最终遗忘效果                                            │
    │                                                                             │
    │  重要理解: cl_matrix[j][k] 表示任务j在全局第(j+k)步的性能                   │
    │  例如: cl_matrix[1][0] = Task1在step1的性能 (不是step0!)                   │
    └─────────────────────────────────────────────────────────────────────────────┘
    """
    # Oracle/LB only evaluates on current task
    if agent.oracle:
        # prepare task files
        task = str(agent.task_id) + "_" + task_list[agent.task_id]["name"]
        out_dir = os.path.join(
            agent.task_dir_dict[task], "_eval-only_" + str(agent.task_id)
        )
        if utils.is_main_process():
            Path(out_dir).mkdir(parents=True, exist_ok=True)
        eval_args["out_dir"] = out_dir
        if eval_args["lb"]:
            eval_args["pretrained"] = agent.model_ckpt_history["pretrained"]
        else:
            eval_args["pretrained"] = agent.model_ckpt_history[task]

        # evaluate the task
        result_file = os.path.join(out_dir, "final_result.yaml")
        will_proceed = True
        if not REVALUATE_FLAG:
            if os.path.exists(result_file):
                try:
                    result = yaml.safe_load(open(result_file, "r"))["result"]
                    if result > 0:
                        will_proceed = False
                    else:
                        will_proceed = True
                except:
                    will_proceed = True
        if will_proceed:
            result = task_trainers.__dict__[task_list[agent.task_id]["trainer"]].main(
                args=eval_args,
                config=agent.task_config_dict[task],
                eval=True,
                test_ema=eval_ema,
            )
            if utils.is_main_process():
                try:
                    result = float(result)
                    result = result.item()
                except:
                    pass
                yaml.dump(
                    {"result": result}, open(result_file, "w"), default_flow_style=False
                )
        if utils.is_main_process():
            result_dict["cl_matrix"][agent.task_id].append(result)

    # evaluate on all seen tasks
    else:
        for t in range(agent.task_id + 1):
            # prepare task files
            task = str(t) + "_" + task_list[t]["name"]
            out_dir = os.path.join(
                agent.task_dir_dict[task], "_eval-only_our" + str(agent.task_id)
            )
            if utils.is_main_process():
                Path(out_dir).mkdir(parents=True, exist_ok=True)
            eval_args["out_dir"] = out_dir
            eval_args["pretrained"] = agent.model_ckpt_list

            # evaluate the task
            result_file = os.path.join(out_dir, "final_result.yaml")
            will_proceed = True
            if not REVALUATE_FLAG:
                if os.path.exists(result_file):
                    try:
                        result = yaml.safe_load(open(result_file, "r"))["result"]
                        if result > 0:
                            will_proceed = False
                        else:
                            will_proceed = True
                    except:
                        will_proceed = True
            if will_proceed:
                result = task_trainers.__dict__[task_list[t]["trainer"]].main(
                    args=eval_args,
                    config=agent.task_config_dict[task],
                    eval=True,
                    test_ema=eval_ema,
                )
                # process the task results
                if utils.is_main_process():
                    try:
                        result = float(result)
                        result = result.item()
                    except:
                        pass
                    yaml.dump(
                        {"result": result},
                        open(result_file, "w"),
                        default_flow_style=False,
                    )

            if utils.is_main_process():
                result_dict["cl_matrix"][t].append(result)

        # post process task eval
        if utils.is_main_process():
            if agent.task_id > 0:
                """
                === 平均遗忘率计算公式详解 ===

                这个公式计算的是"后向传播遗忘率"(Backward Transfer Forgetting)

                公式结构：
                forg = (1/T) * Σ(i=1 to T) [ (1/i) * Σ(j=0 to i-1) [R(j,i-j-1) - R(j,i-j)] ]

                其中：
                - T = agent.task_id (当前任务ID)
                - R(j,k) = result_dict['cl_matrix'][j][k] (任务j训练后第k次评估的性能)
                - i: 时间步 (从1到当前任务)
                - j: 任务ID (从0到i-1)

                ┌─────────────────────────────────────────────────────────────┐
                │  cl_matrix 正确结构可视化                                    │
                │                                                             │
                │  cl_matrix[0] = [R0_step0, R0_step1, R0_step2, R0_step3]   │
                │  cl_matrix[1] = [R1_step1, R1_step2, R1_step3]  ←从step1开始│
                │  cl_matrix[2] = [R2_step2, R2_step3]            ←从step2开始│
                │  cl_matrix[3] = [R3_step3]                      ←从step3开始│
                │                                                             │
                │  关键理解: cl_matrix[j][k] = 任务j在训练后第k次评估的性能    │
                └─────────────────────────────────────────────────────────────┘

                计算步骤详解 (假设当前在Task 3):

                Step 1: i=1 (考虑时间步1)
                  └─ j=0: 比较Task0在其第0次评估 vs 第1次评估
                     cl_matrix[0][1-0-1] vs cl_matrix[0][1-0]
                     = cl_matrix[0][0] vs cl_matrix[0][1]
                     = Task0在step0 vs step1的性能

                Step 2: i=2 (考虑时间步2)
                  ├─ j=0: 比较Task0在其第1次评估 vs 第2次评估
                  │   cl_matrix[0][2-0-1] vs cl_matrix[0][2-0]
                  │   = cl_matrix[0][1] vs cl_matrix[0][2]
                  │   = Task0在step1 vs step2的性能
                  └─ j=1: 比较Task1在其第0次评估 vs 第1次评估
                      cl_matrix[1][2-1-1] vs cl_matrix[1][2-1]
                      = cl_matrix[1][0] vs cl_matrix[1][1]
                      = Task1在step1 vs step2的性能

                Step 3: i=3 (考虑时间步3)
                  ├─ j=0: cl_matrix[0][2] vs cl_matrix[0][3] (Task0 step2→step3)
                  ├─ j=1: cl_matrix[1][1] vs cl_matrix[1][2] (Task1 step2→step3)
                  └─ j=2: cl_matrix[2][0] vs cl_matrix[2][1] (Task2 step2→step3)

                最终：forg = (forg_1 + forg_2 + forg_3) / 3

                ┌─────────────────────────────────────────────────────────────┐
                │  下标含义解释:                                               │
                │                                                             │
                │  cl_matrix[j][i-j-1]: 任务j在全局第(i-1)步的性能            │
                │  cl_matrix[j][i-j]:   任务j在全局第i步的性能                │
                │                                                             │
                │  关键: 每个任务j从第j步开始训练，所以：                      │
                │  - cl_matrix[j][0] = 任务j在第j步的性能                     │
                │  - cl_matrix[j][k] = 任务j在第(j+k)步的性能                 │
                └─────────────────────────────────────────────────────────────┘

                ┌─────────────────────────────────────────────────────────────┐
                │  归一化处理 (如果存在Oracle和Lower Bound):                    │
                │                                                             │
                │  原始遗忘值: to_add = R(j, i-j-1) - R(j, i-j)              │
                │                                                             │
                │  归一化公式: to_add = (to_add - LB) / (Oracle - LB)         │
                │                                                             │
                │  目的: 将遗忘值标准化到[0,1]区间，便于不同实验比较            │
                └─────────────────────────────────────────────────────────────┘
                """
                forg = 0.0
                for i in range(1, agent.task_id + 1):  # tasks through time
                    forg_i = 0.0
                    for j in range(i):  # tasks through task id
                        to_add = (
                            result_dict["cl_matrix"][j][i - j - 1]
                            - result_dict["cl_matrix"][j][i - j]
                        )
                        if oracle_exists:
                            if lb_exists:
                                to_add = (to_add - lb_results[j][0]) / (
                                    oracle_results[j][0] - lb_results[j][0]
                                )
                            else:
                                to_add = to_add / oracle_results[j][0]
                        forg_i += to_add
                    forg += forg_i / i
                forg = forg / agent.task_id
                result_dict["avg_forgetting"][t] = forg

    # record step acc
    # print(agent.task_id)
    # print(result_dict['cl_matrix'])
    # print(result_dict['cl_matrix'][0])
    if utils.is_main_process():
        acc_i = 0.0
        for i in range(0, agent.task_id + 1):
            to_add = result_dict["cl_matrix"][i][-1]
            # print(to_add)

            acc_i += to_add

        acc_cur = acc_i / (agent.task_id + 1)

        result_dict["acc_cur"][agent.task_id] = acc_cur

        # compute final avg_acc
        if agent.task_id == len(task_list) - 1:
            result_dict["acc_cur"][-1] = sum(result_dict["acc_cur"]) / (
                len(result_dict["acc_cur"]) - 1
            )

        """
        === 当前步骤遗忘率计算公式详解 ===
        
        这个公式计算的是"即时遗忘率"(Immediate Forgetting Rate)
        
        公式: forg_cur = (1/T) * Σ(i=0 to T-1) [max(R_i) - R_i[-1]]
        
        其中：
        - T = agent.task_id (当前任务数量)
        - R_i = result_dict['cl_matrix'][i] (任务i的性能历史)
        - max(R_i): 任务i的历史最佳性能
        - R_i[-1]: 任务i的当前最新性能
        
        ┌─────────────────────────────────────────────────────────────────┐
        │  即时遗忘率可视化                                                │
        │                                                                 │
        │  Task 0: [0.8, 0.7, 0.6, 0.5] ← max=0.8, current=0.5          │
        │          遗忘 = 0.8 - 0.5 = 0.3                                │
        │                                                                 │
        │  Task 1: [-, 0.9, 0.8, 0.7]   ← max=0.9, current=0.7          │
        │          遗忘 = 0.9 - 0.7 = 0.2                                │
        │                                                                 │
        │  Task 2: [-, -, 0.85, 0.82]   ← max=0.85, current=0.82        │
        │          遗忘 = 0.85 - 0.82 = 0.03                             │
        │                                                                 │
        │  平均遗忘率 = (0.3 + 0.2 + 0.03) / 3 = 0.177                  │
        │                                                                 │
        │  解释: 衡量每个任务从其历史最佳到当前的性能退化程度              │
        └─────────────────────────────────────────────────────────────────┘
        """
        # compute step (final) forgetting rate
        forg_i = 0.0
        for i in range(1, agent.task_id + 1):
            to_add = (
                max(result_dict["cl_matrix"][i - 1])
                - result_dict["cl_matrix"][i - 1][-1]
            )
            forg_i += to_add
        if agent.task_id == 0:
            forg_cur = 0.0
        else:
            forg_cur = forg_i / (agent.task_id)

        result_dict["forg_cur"][agent.task_id] = forg_cur

        # compute avg step (final) forgetting rate
        if agent.task_id == len(task_list) - 1:
            print("compute avg step (final) forgetting rate")
            result_dict["forg_cur"][-1] = sum(result_dict["forg_cur"]) / (
                len(result_dict["forg_cur"]) - 2
            )

    return result_dict


def evaluate_zeroshot(agent, result_dict, eval_args, task_list, eval_ema):
    # evaluate on all tasks (zero-shot)
    # print('aa' + str(agent.task_id))
    # zero_origin
    # print(task_list)

    for t in range(len(task_list)):
        task = str(t) + "_" + task_list[t]["name"]

        out_dir = os.path.join(
            agent.task_dir_dict[task], "_eval-only_zero" + str(agent.task_id)
        )
        if utils.is_main_process():
            Path(out_dir).mkdir(parents=True, exist_ok=True)
        eval_args["out_dir"] = out_dir
        eval_args["pretrained"] = agent.model_ckpt_list

        result = task_trainers.__dict__[task_list[agent.task_id]["trainer"]].main(
            args=eval_args,
            config=agent.task_config_dict[task],
            eval=True,
            test_ema=eval_ema,
        )

        # process the task results
        if utils.is_main_process():
            try:
                result = float(result)
                result = result.item()
            except:
                pass

        result_dict["zero_shot_acc"][agent.task_id + 1].append(result)

    return result_dict


# train on task sequence
def trainer(args, configs):
    # fix the seed for reproducibility
    torch.backends.cudnn.deterministic = True
    seed = SEED + utils.get_rank()
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    cudnn.benchmark = True

    # init world
    utils.init_distributed_mode(args)
    device = torch.device(args.device)

    # agent_continual_type = args.agent_name
    # args.agent_name = 'Zero_test'

    # create zero-shot agent
    agent_config = {
        "output_dir": args.output_dir,
        "pretrained": configs["pretrained"],
        "oracle": args.oracle_flag or args.lb_flag,
        "mu": args.mu,
        "beta": args.beta,
        "text_only_flag": args.text_only_flag,
        "vision_only_flag": args.vision_only_flag,
        "global_args": args,
        "ema_num_conut": args.ema,
        "type": args.ema,
    }

    agent = agents.__dict__[args.agent_type].__dict__[args.agent_name](agent_config)

    # get tasks
    task_list = configs["task_list"]
    n_tasks = len(task_list)

    # do we have an oracle? used to normalize results for averaging
    oracle_file = os.path.join(
        os.path.dirname(args.output_dir), "UB/final_results/cl_matrix.yaml"
    )
    if not os.path.exists(oracle_file):
        oracle_file = os.path.join(
            os.path.dirname(os.path.dirname(args.output_dir)),
            "UB/final_results/cl_matrix.yaml",
        )
    if not agent.oracle and os.path.exists(oracle_file):
        oracle_exists = True
        oracle_results = yaml.safe_load(open(oracle_file, "r"))
    else:
        oracle_exists = False
        oracle_results = None

    # do we have a LB? used to normalize results for averaging
    lb_file = os.path.join(
        os.path.dirname(args.output_dir), "LB/final_results/cl_matrix.yaml"
    )
    if not os.path.exists(lb_file):
        lb_file = os.path.join(
            os.path.dirname(os.path.dirname(args.output_dir)),
            "LB/final_results/cl_matrix.yaml",
        )
    if not agent.oracle and os.path.exists(lb_file):
        lb_exists = True
        lb_results = yaml.safe_load(open(lb_file, "r"))
    else:
        lb_exists = False
        lb_results = None

    # create results dictionary
    result_dict = {}
    if agent.oracle:
        result_keys = ["cl_matrix"]
    elif oracle_exists:
        result_keys = ["cl_matrix", "final_acc_norm", "avg_acc_norm", "avg_forgetting"]
    else:
        result_keys = [
            "cl_matrix",
            "avg_forgetting",
            "acc_cur",
            "forg_cur",
            "zero_shot_acc",
        ]

    result_dict["cl_matrix"] = [[] for t in range(n_tasks)]
    result_dict["final_acc_norm"] = [-1 for t in range(n_tasks)]
    result_dict["avg_acc_norm"] = [-1 for t in range(n_tasks)]
    result_dict["avg_forgetting"] = [-1 for t in range(n_tasks)]

    result_dict["acc_cur"] = [0 for t in range(n_tasks + 1)]
    result_dict["forg_cur"] = [0 for t in range(n_tasks + 1)]
    result_dict["zero_shot_acc"] = [[] for t in range(n_tasks + 1)]

    print(list(agent.task_dir_dict.keys()))
    agent.init_task(n_tasks, task_list)
    print(list(agent.task_dir_dict.keys()))

    # ema 不用测了，沿用就行
    if not os.path.isdir(os.path.join(args.result_dir, "task_origin")) and False:
        # if not os.path.isdir(os.path.join(args.result_dir, 'task_origin')) and False:
        print("evaluate original zero shot")
        eval_args = {
            "device": device,
            "training_data_sample": args.training_data_sample,
            "distributed": args.distributed,
            "gpu": args.gpu,
            "agent": agent,
            "lb": args.lb_flag,
            "num_workers": args.num_workers,
            "fast_eval": args.fast_eval,
            "flush_queue": args.flush_queue,
        }

        result_dict = evaluate_zeroshot(agent, result_dict, eval_args, task_list, 1)

        if utils.is_main_process():
            save_dir = args.result_dir
            for rkey in result_keys:
                # to_log = log(result_dict['%s'%rkey])
                to_log = log(result_dict[rkey])
                with open(os.path.join(save_dir, rkey + ".txt"), "w") as txt_file:
                    for item in to_log:
                        txt_file.write("%s\n" % item)
                # with open(os.path.join(save_dir, rkey + ".yaml"), 'w') as yaml_file:
                #     yaml.dump(to_log, yaml_file, default_flow_style=False)
            save_dir = os.path.join(args.result_dir, "task_origin")
            os.makedirs(save_dir, exist_ok=True)
            for rkey in result_keys:
                to_log = log(result_dict[rkey])
                with open(os.path.join(save_dir, rkey + ".txt"), "w") as txt_file:
                    for item in to_log:
                        txt_file.write("%s\n" % item)

    # increment over tasks
    for t in range(n_tasks):
        # t=t+1

        # get task
        task = str(t) + "_" + task_list[t]["name"]
        task_config = yaml.load(open(task_list[t]["config"], "r"), Loader=yaml.Loader)

        if args.external_lr >= 0:
            print("Overriding external LR")
            task_config["init_lr"] = args.external_lr

        with open(
            os.path.join(args.output_dir, "config_task-" + task + ".yaml"), "w"
        ) as tcf:
            yaml.dump(task_config, tcf)
        if args.debug_flag:
            task_config["max_epoch"] = 1

        # print(list(agent.task_dir_dict.keys()))

        agent.increment_task(task, task_config)

        # create task args dict
        task_args = {
            "out_dir": agent.task_dir_dict[task],
            "model_load_path": agent.model_ckpt_list,
            # 'model_save_path': agent.model_ckpt_history[agent.tasks[-1]],
            "model_save_path": agent.model_ckpt_history[agent.tasks[agent.task_id]],
            "device": device,
            "training_data_sample": args.training_data_sample,
            "distributed": args.distributed,
            "gpu": args.gpu,
            "pretrained": agent.model_ckpt_load,
            "agent": agent,
            "num_workers": args.num_workers,
            "eval_every": args.eval_every,
            "train_eval_type": args.train_eval_type,
            "flush_queue": args.flush_queue,
        }

        # train task
        training_complete_file = os.path.join(
            agent.task_dir_dict[task], "training_complete.log"
        )
        cur_task_config = agent.task_config_dict[task]
        cur_task_config["task_seq_name"] = task_list[t]["name"]
        cur_task_config["json_files"] = task_list[t].get("json_files", None)
        cur_task_config["task_id_for_debug"] = t
        if not os.path.exists(training_complete_file) and not args.lb_flag:
            if utils.is_main_process():
                print("Start training task + " + str())
            start_time = time.time()
            task_trainers.__dict__[task_list[t]["trainer"]].main(
                args=task_args, config=cur_task_config, eval=False
            )
            total_time = time.time() - start_time
            total_time_str = str(datetime.timedelta(seconds=int(total_time)))
            if utils.is_main_process():
                print("Training time {}".format(total_time_str))
            with open(training_complete_file, "w") as f:
                f.write(total_time_str)

        # rehearsal
        if args.memory > 0:
            agent.coreset.extend(
                task_trainers.__dict__[task_list[t]["trainer"]].sample_memory(
                    memory=args.memory,
                    args=task_args,
                    config=cur_task_config,
                    eval=False,
                )
            )

        # evaluate
        if not os.path.isdir(os.path.join(args.result_dir, f"task_{t:02d}")):
            eval_args = {
                "device": device,
                "training_data_sample": args.training_data_sample,
                "distributed": args.distributed,
                "gpu": args.gpu,
                "agent": agent,
                "lb": args.lb_flag,
                "num_workers": args.num_workers,
                "fast_eval": args.fast_eval,
                "flush_queue": args.flush_queue,
            }
            result_dict = evaluate_tasks(
                agent,
                result_dict,
                eval_args,
                task_list,
                oracle_exists,
                oracle_results,
                lb_exists,
                lb_results,
                agent.ema,
            )
            result_dict = evaluate_zeroshot(
                agent, result_dict, eval_args, task_list, agent.ema
            )

            # save results
            # if utils.is_main_process():
            #     save_dir = args.result_dir
            #     for rkey in result_keys:
            #         with open(os.path.join(save_dir, rkey + ".yaml"), 'w') as yaml_file:
            #             yaml.dump(result_dict[rkey], yaml_file, default_flow_style=False)
            #     save_dir = os.path.join(args.result_dir, 'task_origin')
            #     os.makedirs(save_dir, exist_ok=True)
            #     for rkey in result_keys:
            #         with open(os.path.join(save_dir, rkey + ".yaml"), 'w') as yaml_file:
            #             yaml.dump(result_dict[rkey], yaml_file, default_flow_style=False)

            if utils.is_main_process():
                save_dir = args.result_dir
                for rkey in result_keys:
                    # to_log = log(result_dict['%s'%rkey])
                    to_log = log(result_dict[rkey])
                    with open(os.path.join(save_dir, rkey + ".txt"), "w") as txt_file:
                        for item in to_log:
                            txt_file.write("%s\n" % item)
                    # with open(os.path.join(save_dir, rkey + ".yaml"), 'w') as yaml_file:
                    #     yaml.dump(to_log, yaml_file, default_flow_style=False)
                save_dir = os.path.join(args.result_dir, "task_origin")
                os.makedirs(save_dir, exist_ok=True)
                for rkey in result_keys:
                    to_log = log(result_dict[rkey])
                    with open(os.path.join(save_dir, rkey + ".txt"), "w") as txt_file:
                        for item in to_log:
                            txt_file.write("%s\n" % item)
                    # with open(os.path.join(save_dir, rkey + ".yaml"), 'w') as yaml_file:
                    #     yaml.dump(to_log, yaml_file, default_flow_style=False)
        else:
            prev_task_dir = os.path.join(args.result_dir, f"task_{t:02d}")
            for rkey in result_keys:
                with open(os.path.join(prev_task_dir, rkey + ".yaml"), "r") as f:
                    result_dict[rkey] = yaml.safe_load(f)

        # finish task
        agent.finish_task()


def log(result):
    list = []
    for rkey in result:
        list.append(rkey)
    return list


def get_args():
    parser = argparse.ArgumentParser()

    # benchmark
    parser.add_argument("--config", default="./configs/continual/base.yaml")
    parser.add_argument("--output_dir", default="output/continual")
    parser.add_argument(
        "--repeat", type=int, default=1, help="Repeat the experiment N times"
    )
    parser.add_argument(
        "--overwrite",
        type=int,
        default=0,
        metavar="N",
        help="Train regardless of whether saved model exists",
    )
    parser.add_argument("--device", default="cuda")
    parser.add_argument(
        "--eval_every", type=int, default=1, help="Reduce validation data evals"
    )

    # distributed training
    parser.add_argument(
        "--world_size", default=1, type=int, help="number of distributed processes"
    )
    parser.add_argument(
        "--dist_url", default="env://", help="url used to set up distributed training"
    )
    parser.add_argument("--distributed", default=True, type=bool)
    parser.add_argument(
        "--local_rank",
        default=os.environ.get("LOCAL_RANK", 0),
        type=int,
        help="Please ignore and do not set this argument.",
    )
    parser.add_argument("--debug", action="store_true", help="do debug")
    parser.add_argument("--debug_port", default=12345, type=int, help="for debug")
    parser.add_argument("--num_workers", default=4, type=int, help="for debug")
    parser.add_argument("--debug_addr", type=str, help="for debug")
    parser.add_argument(
        "--training_data_sample",
        default=1.0,
        type=float,
        help="% training data to use.",
    )
    parser.add_argument("--memory", default=0.0, type=float, help="coreset to retain")

    # continual learning
    parser.add_argument(
        "--agent_type", type=str, default="base", help="Base file of continual learner"
    )
    parser.add_argument(
        "--agent_name",
        type=str,
        default="Naive",
        help="Class name of continual learner",
    )
    parser.add_argument(
        "--oracle_flag",
        default=False,
        action="store_true",
        help="Upper bound for oracle",
    )
    parser.add_argument(
        "--lb_flag", default=False, action="store_true", help="Lower bound"
    )
    parser.add_argument(
        "--debug_flag",
        default=False,
        action="store_true",
        help="Debug mode to run faster",
    )
    parser.add_argument("--mu", type=float, default=1.0, help="regularization strength")
    parser.add_argument(
        "--external_lr", type=float, default=-1.0, help="regularization strength"
    )
    parser.add_argument(
        "--beta", type=float, default=0.0, help="regularization strength"
    )
    parser.add_argument(
        "--text_only_flag",
        default=False,
        action="store_true",
        help="only regulalarize text models",
    )
    parser.add_argument(
        "--vision_only_flag",
        default=False,
        action="store_true",
        help="only regularize vision models",
    )
    parser.add_argument(
        "--fast_eval",
        default=False,
        action="store_true",
        help="applies fast eval for multi-lora",
    )
    parser.add_argument(
        "--train_eval_type", type=str, default="slow", help="for multi-lora training"
    )  # slow / fast / last
    parser.add_argument(
        "--loss_alpha", type=float, default=1.0, help="for extra losses"
    )
    parser.add_argument(
        "--auto_scale_alpha",
        default=False,
        action="store_true",
        help="for auto-scaling extra losses",
    )
    parser.add_argument(
        "--skip_base_keep",
        default=False,
        action="store_true",
        help="for not keeping model -1 in adv V2",
    )
    parser.add_argument(
        "--force_keep", type=int, default=None, help="for adv samples CL"
    )
    parser.add_argument(
        "--num_adv_iters", type=int, default=11, help="for adv samples CL"
    )
    parser.add_argument(
        "--adv_step_sz", type=float, default=0.1, help="for adv samples CL"
    )

    # ablations
    parser.add_argument(
        "--adv_last_only", default=False, action="store_true", help="for adv samples CL"
    )
    parser.add_argument(
        "--adv_num_last", type=int, default=1, help="for adv samples CL"
    )
    parser.add_argument(
        "--adv_pos", default=False, action="store_true", help="for adv samples CL"
    )

    # other
    parser.add_argument(
        "--freeze_text_emb", default=False, action="store_true", help="folor ra"
    )
    parser.add_argument(
        "--flush_queue",
        default=False,
        action="store_true",
        help="empty the queue before each task",
    )

    # EMA setting
    parser.add_argument(
        "--ema", type=str, default="task", help="for ema updating"
    )  # task/epoch/mix
    parser.add_argument(
        "--ema_alpha", type=float, default=0.999, help="for epoch ema updating"
    )

    parser.add_argument(
        "--ema_lora", type=str, default="continual", help="for lora initial"
    )  # continual/zero/ema

    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    # debug
    if args.debug:
        set_remote_debugger(debug_port=args.debug_port, debug_ip=args.debug_addr)

    args.output_dir = args.output_dir.format(**args.__dict__)
    print(f"Output dir: {args.output_dir}")

    # configs, output directories, and such
    args.result_dir = os.path.join(args.output_dir, "our_final_results")
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    Path(args.result_dir).mkdir(parents=True, exist_ok=True)
    configs = yaml.load(open(args.config, "r"), Loader=yaml.Loader)
    yaml.dump(configs, open(os.path.join(args.output_dir, "config_sequence.yaml"), "w"))
    yaml.dump(args, open(os.path.join(args.output_dir, "args.yaml"), "w"))

    # let's gooooooo
    trainer(args, configs)
