# --------------------------------------------------------
# Image as a Foreign Language: BEiT Pretraining for Vision and Vision-Language Tasks (https://arxiv.org/abs/2208.10442)
# Github source: https://github.com/microsoft/unilm/tree/master/beit3
# Copyright (c) 2023 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# --------------------------------------------------------'

import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.registry import register_model
import numpy as np
import os
import utils
from beit.modeling_utils import BEiT3Wrapper, _get_base_config, _get_large_config
from transformers import XLMRobertaTokenizer, BertTokenizer



def beit_nlvr(pretrained='',size='',**kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=384,**kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=384,**kwargs)
    else:
        assert False, 'Unknown pretrained model'
    model = BEiT3ForVisualReasoning(args ,num_classes=2, **kwargs)
    head_not_loaded = True
    if pretrained:
        model = load_checkpoint(model,pretrained)
    return model, head_not_loaded


def beit_vqa(pretrained='',size='',**kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=480, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=480, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    args.normalize_output = False
    model = BEiT3ForVisualQuestionAnswering(args, num_classes=64010, **kwargs)
    head_not_loaded = True
    if pretrained:
        model = load_checkpoint(model,pretrained)
    return model, head_not_loaded



class Pooler(nn.Module):
    def __init__(self, input_features, output_features, norm_layer):
        super().__init__()
        self.norm = norm_layer(input_features)
        self.dense = nn.Linear(input_features, output_features)
        self.activation = nn.Tanh()

    def forward(self, x):
        cls_rep = x[:, 0, :]
        cls_rep = self.norm(cls_rep)
        pooled_output = self.dense(cls_rep)
        pooled_output = self.activation(pooled_output)
        return pooled_output


class BEiT3ForVisualReasoning(BEiT3Wrapper):
    def __init__(
            self, 
            args, 
            num_classes, 
            norm_layer=nn.LayerNorm,
            agent=None,
            **kwargs
    ):
        super(BEiT3ForVisualReasoning, self).__init__(args=args,agent= agent)
        embed_dim = args.encoder_embed_dim
        self.num_max_bpe_tokens = 64
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim , embed_dim ),
            nn.ReLU(),
            nn.Linear(embed_dim, num_classes)
        )
        self.pooler = Pooler(
            input_features=embed_dim,
            output_features=embed_dim,
            norm_layer=norm_layer,
        )
        self.pooler.apply(self._init_weights)
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            norm_layer(embed_dim * 2),
            nn.GELU(),
            nn.Linear(embed_dim * 2, num_classes),
        )
        self.cls_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def forward(self, image, text_description, targets, train=True, agent=None, feature_forward=False, train_zsl = False,wild_id = None, **kwargs):
        all_tokens = []
        all_token_ids = []
        all_language_tokens = []
        all_padding_masks = []

        for text in text_description:
            # 对每个文本进行分词
            tokens = self.tokenizer.tokenize(text)
            # 将分词结果转换为输入 ID
            token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
            # 获取文本片段、填充掩码等信息
            language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
            # 将结果存储到对应的列表中
            all_tokens.append(tokens)
            all_token_ids.append(token_ids)
            all_language_tokens.append(language_tokens)
            all_padding_masks.append(padding_mask)

        all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
        all_padding_masks = torch.tensor(all_padding_masks, dtype=torch.long).to(image.device)

        outputs = self.beit3(
            textual_tokens=all_language_tokens,
            visual_tokens=image,
            text_padding_position=all_padding_masks,
        )
        x = outputs["encoder_out"]
        cls_rep = self.pooler(x)
        prediction = self.cls_head(cls_rep)
        if train:
            if not train_zsl:
                loss = F.cross_entropy(prediction, targets)
            return loss
        else:
            return prediction

    

class BEiT3ForVisualQuestionAnswering(BEiT3Wrapper):
    def __init__(
            self, 
            args, 
            num_classes, 
            norm_layer=nn.LayerNorm,
            agent=None,
            **kwargs
    ):
        super(BEiT3ForVisualQuestionAnswering, self).__init__(args=args,agent= agent)
        embed_dim = args.encoder_embed_dim
        self.num_max_bpe_tokens = 64
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.pooler = Pooler(
            input_features=embed_dim, 
            output_features=embed_dim, 
            norm_layer=norm_layer, 
        )
        # self.pooler.apply(self._init_weights)
        # self.cls_head = nn.Sequential(
        #     nn.Linear(embed_dim, embed_dim * 2),
        #     norm_layer(embed_dim * 2),
        #     nn.GELU(),
        #     nn.Linear(embed_dim * 2, num_classes),
        # )
        # self.cls_head.apply(self._init_weights)

        self.cls_head = nn.Linear(embed_dim, num_classes)
        self.cls_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def forward(self, image, question, answer=None, n=None, weights=None, train=True, inference=None, incremental_state=None, k_test=128, train_zsl=False, agent = None,wild_id = None, **kwargs):
        if train:
            if incremental_state is None:
                all_tokens = []
                all_token_ids = []
                all_language_tokens = []

                for text in question:
                    # 对每个文本进行分词
                    tokens = self.tokenizer.tokenize(text)
                    # 将分词结果转换为输入 ID
                    token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
                    # 获取文本片段、填充掩码等信息
                    language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
                    # 将结果存储到对应的列表中
                    all_tokens.append(tokens)
                    all_token_ids.append(token_ids)
                    all_language_tokens.append(language_tokens)

                all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)

                question_all = []
                for b, i in enumerate(n):
                    question_all += [all_language_tokens[b]] * i
                question = torch.stack(question_all, 0)

                image_all = []
                for b, i in enumerate(n):
                    image_all += [image[b]] * i
                image = torch.stack(image_all, 0).to(image.device)

                all_answer_ids = []
                for text in answer:
                    tokens = self.tokenizer.tokenize(text)
                    token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
                    language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
                    all_answer_ids.append(language_tokens)

                labels = torch.tensor(all_answer_ids, dtype=torch.long).to(image.device)

                batch_size = question.size(0)
                ques_len = question.size(1)

                # answer = self.tokenizer(answer, padding='longest', return_tensors="pt").to(image.device)
                # answer.input_ids[:, 0] = self.tokenizer.bos_token_id
                # answer_targets = answer.input_ids.masked_fill(answer.input_ids == self.tokenizer.pad_token_id, -100)

                # 将padding标签替换为 -100 (忽略计算损失)
                labels = labels.masked_fill(labels == self.pad_token_id, -100)
                # 答案序列去除最后一个token（此应为<EOS>），并在前面加<BOS>
                bos_tokens = torch.full((batch_size, 1), self.bos_token_id, dtype=torch.long, device=image.device)
                ans_input = labels.clone()
                # labels 此时包含EOS和内容, 先取出答案序列（将-100暂视作pad处理，不影响前移）
                # 注意：labels的最后一个有效token应为EOS（非-100）,前移时将EOS移入序列内
                ans_without_pad = ans_input
                # 若 labels 包含 -100，需要将它们替换成 pad_token_id 再用于构建输入（因为-100非真实词表ID）
                ans_without_pad[ans_without_pad == -100] = self.pad_token_id
                answer_input_ids = torch.cat([bos_tokens, ans_without_pad[:, :-1]], dim=1)  # [BOS][答[:-1]]

                text_inputs = torch.cat([question, answer_input_ids], dim=1)
                ques_padding = (question == self.pad_token_id).long()  # 1表示PAD
                ans_padding = (answer_input_ids == self.pad_token_id).long()
                text_padding_mask = torch.cat([ques_padding, ans_padding], dim=1)  # [batch, total_text_len]
                # 构造注意力mask (uni_mask): 允许的注意力位置标记为1，稍后取反得到attn_mask
                total_text_len = text_inputs.size(1)
                # 图像token长度
                image_len = self.beit3.vision_embed.num_position_embeddings()  # 获取图像序列长度
                total_len = image_len + total_text_len
                # 初始化全零的允许矩阵
                uni_mask = torch.zeros((total_len, total_len), dtype=torch.long, device=image.device)
                # 定义各分段索引
                img_start, img_end = 0, image_len
                ques_start, ques_end = image_len, image_len + ques_len
                ans_start, ans_end = image_len + ques_len, total_len  # ans_end 索引在总序列上为 total_len-1 (切片用total_len包含)
                # 1) 图像->图像 允许
                uni_mask[img_start:img_end, img_start:img_end] = 1
                # 2) 问题->问题 允许
                uni_mask[ques_start:ques_end, ques_start:ques_end] = 1
                # 3) 图像<->问题 允许 (双向都允许融合)
                uni_mask[img_start:img_end, ques_start:ques_end] = 1
                uni_mask[ques_start:ques_end, img_start:img_end] = 1
                # 4) 答案->答案 只允许看见自己和之前（下三角，包括对角线）
                ans_len = ans_end - ans_start  # 答案输入总长度 (包含<BOS>和可能的<EOS>)
                uni_mask[ans_start:ans_end, ans_start:ans_end] = torch.tril(
                    torch.ones((ans_len, ans_len), dtype=torch.long, device=image.device))
                # 5) 答案->图像 允许
                uni_mask[ans_start:ans_end, img_start:img_end] = 1
                # 6) 答案->问题 允许
                uni_mask[ans_start:ans_end, ques_start:ques_end] = 1
                # 7) 上下文->答案 不允许 (保持为0即可，不用显式设置)
                # 最后，翻转mask：1变0，0变1，得到attn_mask，其中1表示禁止关注的位置
                attn_mask = 1 - uni_mask

            outputs = self.beit3(
                textual_tokens=text_inputs,
                visual_tokens=image,
                text_padding_position=text_padding_mask,  # 文本padding位置
                attn_mask=attn_mask,
                incremental_state=None,  # 训练时不使用增量状态
            )
            text_feats = outputs["encoder_out"][:, image_len:, :]
            # 通过 mlm_head 得到每个位置的词表分布 logits
            prediction = self.mlm_head(text_feats)

            labels = labels.to(prediction.device)
            # **Shifted Prediction**: 对齐预测和标签，将预测右移一位与下一个词标签对齐
            shifted_prediction_scores = prediction[:, :-1, :].contiguous()  # 去除最后一个时间步的预测
            shifted_labels = labels[:, 1:].contiguous()  # 去除标签序列开头的<BOS>（或问题末尾）
            # 配置损失函数
            loss_fct = nn.CrossEntropyLoss(reduction='none', label_smoothing=0.1)
            # 将 (batch, seq_len-1, vocab) 展平为 (batch*(seq_len-1), vocab)，标签展平为相应长度
            loss = loss_fct(shifted_prediction_scores.view(-1, shifted_prediction_scores.size(-1)),
                            shifted_labels.view(-1))
            return loss
        else:
            return self.generate(image, question, n)

    def generate(self, image, question,n, num_beams=3, max_length=10, incremental_state=None):
        if incremental_state == None:
            all_tokens = []
            all_token_ids = []
            all_language_tokens = []

            for text in question:
                # 对每个文本进行分词
                tokens = self.tokenizer.tokenize(text)
                # 将分词结果转换为输入 ID
                token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
                # 获取文本片段、填充掩码等信息
                language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
                # 将结果存储到对应的列表中
                all_tokens.append(tokens)
                all_token_ids.append(token_ids)
                all_language_tokens.append(language_tokens)

            all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)

            question_all = []
            for b, i in enumerate(n):
                question_all += [all_language_tokens[b]] * i
            question = torch.stack(question_all, 0)

            image_all = []
            for b, i in enumerate(n):
                image_all += [image[b]] * i
            image = torch.stack(image_all, 0).to(image.device)

        """
        使用Beam Search生成答案序列。返回每个batch输入对应生成的答案token序列（不含<BOS>和<EOS>）。
        """
        self.eval()  # 推理模式
        batch_size = question.size(0)
        ques_len = question.size(1)
        # 初始化增量状态字典（各层缓存）
        if incremental_state is None:
            incremental_state = {}
        # 准备初始输入：在问题后附加<BOS>作为答案起始
        bos_tokens = torch.full((batch_size, 1), self.bos_token_id, dtype=torch.long, device=question.device)
        # Note: 假定问题question结尾已包含</s>，此处直接拼接<BOS>
        current_sequences = torch.cat([question, bos_tokens], dim=1)  # 初始输入序列 (含问题和<BOS>)
        # Beam候选：存储每个beam的token序列及其累积log概率
        # 对每个输入初始化beam列表，其中每个beam包含初始<BOS>序列
        beams = [{"tokens": current_sequences[i:i + 1], "log_prob": 0.0, "incremental_state": {}}
                 for i in range(batch_size)]
        # 如果batch_size > 1，这里需对每个样本分别进行Beam Search。为简单起见假设单个样本调用。
        # 多个样本情况下，可在循环外部套batch，但此处演示单一样本。
        final_output = []
        for beam_idx in range(num_beams):
            final_output.append([])  # 占位

        # 由于此处主要演示，将batch视为大小1的情况:
        # 初始化beam列表 (长度num_beams)，以第一个beam为基础展开为num_beams个
        initial_beam = beams[0]
        beams = [initial_beam.copy() for _ in range(num_beams)]
        # 为每个beam复制相同的初始增量状态
        for i in range(num_beams):
            beams[i]["incremental_state"] = {layer: state.copy() for layer, state in
                                             initial_beam["incremental_state"].items()}

        # 生成循环
        generated = None  # 最佳序列tokens
        for step in range(max_length):
            # 扩展每个beam一步
            all_candidates = []
            for beam in beams:
                # 若已经生成EOS，则跳过扩展
                seq_tensor = beam["tokens"]  # shape [1, seq_len]
                if int(seq_tensor[0, -1]) == self.eos_token_id:
                    # 保持EOS序列，候选不再扩展
                    all_candidates.append(beam)
                    continue
                # 调用模型forward获取下一个token的概率分布
                # 利用增量状态，使模型只计算新增token
                outputs = self.forward(image, seq_tensor[:, :-1], labels=seq_tensor[:, 1:],
                                       incremental_state=beam["incremental_state"], inference='')  # 特别地，这里直接使用forward简化
                # outputs["logits"] shape: [1, seq_len, vocab_size]
                logits = outputs["logits"][0, -1, :]  # 取当前序列最后一个token的预测分布
                # 计算 log softmax 作为概率
                probs = F.log_softmax(logits, dim=-1)  # 对数概率
                # 获取 topK 候选
                topk_probs, topk_indices = torch.topk(probs, num_beams)
                for k in range(num_beams):
                    new_token_id = topk_indices[k].unsqueeze(0).unsqueeze(0)  # [[token]]
                    new_log_prob = beam["log_prob"] + float(topk_probs[k])
                    # 新序列 = 当前序列 + 新token
                    new_seq = torch.cat([seq_tensor, new_token_id], dim=1)
                    # 复制增量状态，以免不同候选互相干扰
                    new_inc_state = {layer: state.copy() for layer, state in beam["incremental_state"].items()}
                    # 将新token加入增量缓存（通过再次forward或其他方式更新 incremental_state）
                    # 这里再次调用模型一步，以更新 incremental_state
                    _ = self.beit3(textual_tokens=new_token_id, visual_tokens=None,
                                   text_padding_position=None,
                                   attn_mask=torch.zeros((1, 1), device=new_token_id.device),
                                   incremental_state=new_inc_state,
                                   positions=torch.arange(new_seq.size(1) - 1, new_seq.size(1),
                                                          device=new_token_id.device).unsqueeze(0))
                    # 保存候选
                    all_candidates.append({
                        "tokens": new_seq,
                        "log_prob": new_log_prob,
                        "incremental_state": new_inc_state
                    })
            # 从所有候选中选取累计对数概率最高的 num_beams 个
            all_candidates.sort(key=lambda x: x["log_prob"], reverse=True)
            beams = all_candidates[:num_beams]
            # 检查是否有序列完成 (输出EOS)
            finished_beams = [beam for beam in beams if int(beam["tokens"][0, -1]) == self.eos_token_id]
            if finished_beams:
                # 选取分数最高的完成序列作为生成结果
                best_beam = max(finished_beams, key=lambda x: x["log_prob"])
                generated = best_beam["tokens"][0]  # [seq_len] 张量
                break
        # 若循环完仍无EOS，则取当前最高分序列
        if generated is None:
            best_beam = max(beams, key=lambda x: x["log_prob"])
            generated = best_beam["tokens"][0]
        # 移除开头的问题部分和<BOS>, 以及结尾的<EOS>
        # 问题长度question.size(1)，再跳过<BOS>
        gen_tokens = generated[ques_len + 1:]  # 跳过问题和<BOS>
        # 如果最后一位是EOS则去掉
        if gen_tokens[-1].item() == self.eos_token_id:
            gen_tokens = gen_tokens[:-1]

        # answers = []
        # for output in gen_tokens:
        #     output_ids = output.argmax(dim=-1)
        answers = self.tokenizer.decode(gen_tokens, skip_special_tokens=True)
            # answers.append(answer)

        return answers

def load_checkpoint(model, url_or_filename_list, prefix='', ignore_missing="relative_position_index"):
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError('checkpoint url or path is invalid')
            state_dict = checkpoint['model']

    missing_keys = []
    unexpected_keys = []
    error_msgs = []
    # copy state_dict so _load_from_state_dict can modify it
    metadata = getattr(state_dict, '_metadata', None)
    state_dict = state_dict.copy()
    if metadata is not None:
        state_dict._metadata = metadata

    def load(module, prefix=''):
        local_metadata = {} if metadata is None else metadata.get(
            prefix[:-1], {})
        module._load_from_state_dict(
            state_dict, prefix, local_metadata, True, missing_keys, unexpected_keys, error_msgs)
        for name, child in module._modules.items():
            if child is not None:
                load(child, prefix + name + '.')

    load(model, prefix=prefix)

    warn_missing_keys = []
    ignore_missing_keys = []
    for key in missing_keys:
        keep_flag = True
        for ignore_key in ignore_missing.split('|'):
            if ignore_key in key:
                keep_flag = False
                break
        if keep_flag:
            warn_missing_keys.append(key)
        else:
            ignore_missing_keys.append(key)

    missing_keys = warn_missing_keys

    if len(missing_keys) > 0:
        print("Weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, missing_keys))
    if len(unexpected_keys) > 0:
        print("Weights from pretrained model not used in {}: {}".format(
            model.__class__.__name__, unexpected_keys))
    if len(ignore_missing_keys) > 0:
        print("Ignored weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, ignore_missing_keys))
    if len(error_msgs) > 0:
        print('\n'.join(error_msgs))

    return model
