# --------------------------------------------------------
# Image as a Foreign Language: BEiT Pretraining for Vision and Vision-Language Tasks (https://arxiv.org/abs/2208.10442)
# Github source: https://github.com/microsoft/unilm/tree/master/beit3
# Copyright (c) 2023 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# --------------------------------------------------------'
from torch.nn.utils.rnn import pad_sequence
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.registry import register_model
import numpy as np
import os
# import utils
from beit.modeling_utils import BEiT3Wrapper, _get_base_config, _get_large_config
from transformers import XLMRobertaTokenizer, BertTokenizer
from models.med import Bert<PERSON><PERSON>fi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Model
from models.blip import create_vit, init_tokenizer
from urllib.parse import urlparse
from beit.glossary import normalize_word
from beit.utils import BeamHypotheses
def beit_nlvr(pretrained='', size='', **kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=384, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=384, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    model = BEiT3ForVisualReasoning(args, num_classes=2, **kwargs)
    head_not_loaded = True
    if pretrained:
        model = load_checkpoint(model, pretrained)
    return model, head_not_loaded


def beit_vqa(pretrained='', size='', task_id = -1, **kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=480, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=480, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    args.normalize_output = False
    model = BEiT3ForVisualQuestionAnswering(args, **kwargs)
    head_not_loaded = True
    # if pretrained:
    #     model = load_checkpoint(model, pretrained)
    if pretrained:
        # if task_id != -1 and task_id == 0:
        #     # model,msg = load_checkpoint_blip(model, ['/root/xingxing/ckpt/model_base.pth'])
        #     model,msg = load_checkpoint_blip(model, ['/root/xingxing/ckpt/model_base_vqa_capfilt_large.pth'])
        model = load_checkpoint(model, pretrained)

    return model, head_not_loaded


class Pooler(nn.Module):
    def __init__(self, input_features, output_features, norm_layer):
        super().__init__()
        self.norm = norm_layer(input_features)
        self.dense = nn.Linear(input_features, output_features)
        self.activation = nn.Tanh()

    def forward(self, x):
        cls_rep = x[:, 0 , :]
        cls_rep = self.norm(cls_rep)
        pooled_output = self.dense(cls_rep)
        pooled_output = self.activation(pooled_output)
        return pooled_output

class Pooler_v2(nn.Module):
    def __init__(self, input_features, output_features, norm_layer):
        super().__init__()
        self.norm = norm_layer(input_features)
        self.dense = nn.Linear(input_features, output_features)
        self.activation = nn.Tanh()

    def forward(self, x):
        # cls_rep = x[:, : , :]
        cls_rep = self.norm(x)
        pooled_output = self.dense(cls_rep)
        pooled_output = self.activation(pooled_output)
        return pooled_output

class BEiT3ForVisualReasoning(BEiT3Wrapper):
    def __init__(
            self,
            args,
            num_classes,
            norm_layer=nn.LayerNorm,
            agent=None,
            **kwargs
    ):
        super(BEiT3ForVisualReasoning, self).__init__(args=args, agent=agent)
        embed_dim = args.encoder_embed_dim
        self.num_max_bpe_tokens = 64
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.pooler = Pooler(
            input_features=embed_dim,
            output_features=embed_dim,
            norm_layer=norm_layer,
        )
        self.pooler.apply(self._init_weights)
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            norm_layer(embed_dim * 2),
            nn.GELU(),
            nn.Linear(embed_dim * 2, num_classes),
        )
        self.cls_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def forward(self, image, text_description, targets, train=True, agent=None, feature_forward=False, train_zsl=False,
                wild_id=None, **kwargs):
        all_tokens = []
        all_token_ids = []
        all_language_tokens = []
        all_padding_masks = []

        for text in text_description:
            # 对每个文本进行分词
            tokens = self.tokenizer.tokenize(normalize_word(text))
            # 将分词结果转换为输入 ID
            token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
            # 获取文本片段、填充掩码等信息
            language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
            # 将结果存储到对应的列表中
            all_tokens.append(tokens)
            all_token_ids.append(token_ids)
            all_language_tokens.append(language_tokens)
            all_padding_masks.append(padding_mask)

        all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
        all_padding_masks = torch.tensor(all_padding_masks, dtype=torch.long).to(image.device)

        outputs = self.beit3(
            textual_tokens=all_language_tokens,
            visual_tokens=image,
            text_padding_position=all_padding_masks,
        )
        x = outputs["encoder_out"]
        cls_rep = self.pooler(x)
        prediction = self.cls_head(cls_rep)
        if train:
            if not train_zsl:
                loss = F.cross_entropy(prediction, targets)
            return loss
        else:
            return prediction

class BEiT3ForVisualQuestionAnswering(BEiT3Wrapper):
    def __init__(
            self,
            args,
            agent=None,
            norm_layer=nn.LayerNorm,
            **kwargs
    ):
        super(BEiT3ForVisualQuestionAnswering, self).__init__(args=args,agent=agent)
        embed_dim = args.encoder_embed_dim
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.num_max_bpe_tokens = 32
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.mask_token_id = self.tokenizer.mask_token_id

        self.vocab_size = args.vocab_size

        self.pooler = Pooler_v2(
            input_features=embed_dim,
            output_features=embed_dim,
            norm_layer=norm_layer,
        )
        self.pooler.apply(self._init_weights)

        self.mlm_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            norm_layer(embed_dim * 2),
            nn.GELU(),
            nn.Linear(embed_dim * 2, args.vocab_size),
        )
        self.mlm_head.apply(self._init_weights)

        # 添加MLM语言模型头替换原分类头
        # self.mlm_head = nn.Linear(embed_dim, args.vocab_size)
        # self.mlm_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def process_id(self, text_seqment):
        all_ids = []
        all_len = []
        for x, text in enumerate(text_seqment):
            tokens = self.tokenizer.tokenize(text)
            token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
            all_ids.append(token_ids)
            all_len.append(len(token_ids))
        return all_ids, all_len

    def process_id_infer(self, text_seqment):
        all_ids = []
        all_len = []
        for x, text in enumerate(text_seqment):
            tokens = self.tokenizer.tokenize(text)
            token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
            all_ids.append([self.bos_token_id] + token_ids[:])
            all_len.append(len(token_ids))
        return all_ids, all_len
    #把答案对应的token置为1，代表被mask
    def _masking_on_text_tokens(self, tokens, question_len, answer_len):
        bool_masked_pos = [0] * len(tokens)
        begin_pos = question_len + 1
        # end_pos = 1 + question_len + answer_len
        end_pos = 1 + question_len + answer_len
        if end_pos > len(tokens):
            print(end_pos,len(tokens))
            end_pos = len(tokens)
        # end_pos = len(tokens) - 2
        for i in range(begin_pos, end_pos):
            #记录下被mask掉的答案对应的pos
            bool_masked_pos[i] = 1
            # 把答案的位置置为1，变成mask，不把答案输入模型
            tokens[i] = self.mask_token_id

        return tokens, bool_masked_pos

    def forward(self, image, text_ids, answer=None, train=False, n=None, padding_mask=None, language_masked_pos=None, incremental_state=None, text_len=None, weights=None, agent=None, **kwargs):
        if incremental_state is None:
            #根据n来处理answer和weights
            question_all = []
            for b, i in enumerate(n):
                question_all += [text_ids[b]] * i
            text_ids = question_all

            image_all = []
            for b, i in enumerate(n):
                image_all += [image[b]] * i
            image = torch.stack(image_all, 0).to(image.device)

            #先得到他们所有的token_id
            all_question_ids, question_len = self.process_id(text_ids)
            all_answer_ids, answer_len = self.process_id(answer)

            #得到question+answer的token_id以及对应的padding mask
            language_tokens = []
            all_padding_mask = []
            all_num_tokens = []
            for i, question_id in enumerate(all_question_ids):
                tokens, padding_mask, num_tokens = self._get_text_segment(question_id + all_answer_ids[i])
                language_tokens.append(tokens)
                all_padding_mask.append(padding_mask)
                all_num_tokens.append(num_tokens)

            #Mask掉所有answer，同时记录被mask的answer的位置的id（Bool值）
            all_masked_tokens = []
            all_language_masked_pos = []
            # answer_start_idx = []
            # answer_end_idx = []
            for i, token in enumerate(language_tokens):
                process_token = token[:]
                masked_tokens, language_masked_pos = self._masking_on_text_tokens(process_token, question_len[i],answer_len[i])
                # answer_start_idx.append(question_len[i] + 2)
                # answer_end_idx.append(question_len[i] + answer_len[i] + 2)
                all_masked_tokens.append(masked_tokens)
                all_language_masked_pos.append(language_masked_pos)

            # answer_start_idx = torch.tensor(answer_start_idx, dtype=torch.long).to(image.device)
            # answer_end_idx = torch.tensor(answer_end_idx, dtype=torch.long).to(image.device)

            language_tokens = torch.tensor(language_tokens, dtype=torch.long).to(image.device)
            padding_mask = torch.tensor(all_padding_mask, dtype=torch.long).to(image.device)
            text_ids = torch.tensor(all_masked_tokens, dtype=torch.long).to(image.device)
            language_masked_pos = torch.tensor(all_language_masked_pos, dtype=torch.long).to(image.device)

        text_len = text_len if text_len is not None else text_ids.size(1)
        image_len = self.beit3.vision_embed.num_position_embeddings()
        max_len = text_len + image_len
        uni_mask = torch.zeros((max_len, max_len), dtype=torch.long, device=text_ids.device)
        i_start, i_end = 0, image_len
        t_start, t_end = image_len, max_len
        # triangle mask for caption to caption
        uni_mask[t_start:t_end, t_start:t_end] = torch.tril(
            torch.ones(text_len, text_len, dtype=torch.long, device=text_ids.device))
        # full attention for caption to image
        uni_mask[t_start:t_end, i_start:i_end] = 1
        # full attention for image to image
        uni_mask[i_start:i_end, i_start:i_end] = 1
        uni_mask = 1 - uni_mask

        if incremental_state is not None:
            for idx in range(self.get_num_layers()):
                if idx not in incremental_state:
                    incremental_state[idx] = {}

        # for incremental decoding
        positions = None
        if image is None:
            uni_mask = uni_mask[-2:]
            padding_mask = None
            # padding_mask = padding_mask
            # start position (2 (fairseq starts at 2) + cur_position) is equal to text_len
            positions = torch.arange(text_len, text_ids.size(1) + text_len,
                                     device=text_ids.device).long().unsqueeze(0)

        outputs = self.beit3(
            textual_tokens=text_ids,
            visual_tokens=image,
            text_padding_position=padding_mask,
            attn_mask=uni_mask,
            incremental_state=incremental_state,
            positions=positions,
        )
        if image is not None:
            text_feats = outputs["encoder_out"][:, image_len:]
        else:
            text_feats = outputs["encoder_out"]

        #text_feats数量级特别大，所以加 F.layer_norm
        # text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
        # text_feats = F.normalize(text_feats,dim=-1)
        if language_masked_pos is not None:
            text_feats = text_feats[language_masked_pos.bool()]

        logits = self.mlm_head(self.pooler(text_feats))

        if train:
            masked_labels = language_tokens[language_masked_pos.bool()]
            # label_smoothing = 0.1
            # n_class = logits.size(1)
            # one_hot = torch.zeros_like(logits).scatter(1, masked_labels.view(-1, 1), 1)
            # one_hot = one_hot * (1 - label_smoothing) + (1 - one_hot) * label_smoothing / (n_class - 1)
            # log_prb = nn.LogSoftmax(dim=1)(logits)
            # loss_kl = nn.KLDivLoss(reduction='none')(log_prb, one_hot).sum(1)

            loss_fct = nn.CrossEntropyLoss(reduction='none', label_smoothing=0.1)
            lm_loss = loss_fct(logits, masked_labels)

            # weighted_loss = []
            start = 0
            loss = torch.tensor(0.0, device=image.device)
            for i, length in enumerate(answer_len):
                end = start + length
                group_loss = lm_loss[start:end].sum()  # 每一组内取和
                loss = loss + group_loss * weights[i]  # 加权
                start = end
            # 所有组加权求和
            loss = loss / image.size(0)
            return loss
        else:
            return logits, incremental_state

    def generate(self, images, questions):
        self.num_beams = 3
        self.max_len = self.num_max_bpe_tokens
        # self.max_len = 16
        all_answer = []
        for image, question in zip(images,questions):
            tokens = self.tokenizer.tokenize(question)
            question_id = self.tokenizer.convert_tokens_to_ids(tokens)
            question_len = len(question_id)
            question_id = torch.tensor([[self.bos_token_id] + question_id], dtype=torch.long).to(image.device)

            cur_len = question_len + 1 + 1

            num_keep_best = 1
            TOPN_PER_BEAM = 3
            batch_size = 1
            mask_id = self.tokenizer.mask_token_id
            # cls_id = self.tokenizer.cls_token_id
            pad_id = self.tokenizer.pad_token_id
            sep_id = self.tokenizer.sep_token_id
            eos_token_ids = [sep_id]

            # cls_ids = torch.full(
                # (batch_size, 1), cls_id, dtype=torch.long, device=image.device
            # )

            mask_ids = torch.full(
                (batch_size, 1), mask_id, dtype=torch.long, device=image.device
            )
            cur_input_ids = torch.cat([question_id, mask_ids], dim=1)
            tmp_ids = torch.full(
                (batch_size, self.max_len - (cur_len - 1) ), mask_id, dtype=torch.long, device=image.device
            )
            decoding_results = torch.cat([question_id, tmp_ids], dim=1)

            # Expand input to num beams
            cur_input_ids = cur_input_ids.unsqueeze(1).expand(batch_size, self.num_beams, cur_len)
            cur_input_ids = cur_input_ids.contiguous().view(batch_size * self.num_beams,
                                                            cur_len)  # (batch_size * num_beams, cur_len)
            decoding_results = decoding_results.unsqueeze(1).expand(batch_size, self.num_beams, self.max_len)
            decoding_results = decoding_results.contiguous().view(batch_size * self.num_beams,
                                                                  self.max_len)  # (batch_size * num_beams, cur_len)
            image = image.unsqueeze(1).expand(batch_size, self.num_beams, image.size(-3), image.size(-2), image.size(-1))
            image = image.contiguous().view(batch_size * self.num_beams, image.size(-3), image.size(-2), image.size(-1))

            generated_hyps = [
                BeamHypotheses(
                    num_keep_best, self.max_len, length_penalty=0.6, early_stopping=False
                ) for _ in range(batch_size)
            ]
            # scores for each sentence in the beam
            beam_scores = torch.zeros((batch_size, self.num_beams), dtype=torch.float, device=cur_input_ids.device)
            beam_scores[:, 1:] = -1e9
            beam_scores = beam_scores.view(-1)  # shape (batch_size * num_beams,)

            # done sentences
            done = [False for _ in range(batch_size)]
            incremental_state = {}

            begin_lengh = question_len + 1 + 1

            while cur_len <= self.max_len:
                if cur_len != begin_lengh:
                    #在这里不能是原来的2了，我发现incremental state里面会带着之前的token的数量，比如原来是901+8，则incremental里面会基于这个90再去预测下一个，
                    # 所以我也需要给他们的mask前面padding上对应的长度以达成喝上一步一样的值
                    input_image = None
                    # next_token_idx = 1
                    next_token_idx += 1
                    # length = 2
                    length +=1
                    padding_masks = torch.full(
                        cur_input_ids.shape, 0, dtype=torch.long, device=image.device
                    )
                else:
                    input_image = image
                    next_token_idx = question_len + 1
                    length = question_len + 1 + 1
                    padding_masks = torch.full(
                        cur_input_ids.shape, 0, dtype=torch.long, device=image.device
                    )
                    for padding_mask in padding_masks:
                        padding_mask[:question_len] = 1

                outputs, incremental_state_next = self.forward(
                    image=input_image, text_ids=cur_input_ids, language_masked_pos=None,
                    padding_mask=padding_masks, text_len=length, incremental_state=incremental_state)
                incremental_state = incremental_state_next
                if cur_len != begin_lengh:
                    next_token_idx = 1
                # assert outputs.shape[1] == token_len
                scores = outputs[:, next_token_idx, :]  # (batch_size * num_beams, vocab_size)
                scores = F.log_softmax(scores, dim=-1)  # (batch_size * num_beams, vocab_size)
                assert scores.size() == (batch_size * self.num_beams, self.vocab_size)
                # Add the log prob of the new beams to the log prob of the beginning of the sequence (sum of logs == log of the product)
                _scores = scores + beam_scores[:, None].expand_as(scores)  # (batch_size * num_beams, vocab_size)
                # re-organize to group the beam together (we are keeping top hypothesis accross beams)
                _scores = _scores.view(batch_size, self.num_beams * self.vocab_size)  # (batch_size, num_beams * vocab_size)
                next_scores, next_words = torch.topk(_scores, TOPN_PER_BEAM * self.num_beams, dim=1, largest=True,
                                                     sorted=True)
                assert next_scores.size() == next_words.size() == (batch_size, TOPN_PER_BEAM * self.num_beams)

                # next batch beam content
                # list of (batch_size * num_beams) tuple(next hypothesis score, next word, current position in the batch)
                next_batch_beam = []
                # for each sentence
                for batch_ex in range(batch_size):
                    # if we are done with this sentence
                    done[batch_ex] = done[batch_ex] or generated_hyps[batch_ex].is_done(next_scores[batch_ex].max().item())
                    if done[batch_ex]:
                        next_batch_beam.extend([(0, pad_id, 0)] * self.num_beams)  # pad the batch
                        continue

                    # next sentence beam content
                    next_sent_beam = []
                    for idx, score in zip(next_words[batch_ex], next_scores[batch_ex]):
                        # get beam and word IDs
                        beam_id = idx // self.vocab_size
                        word_id = idx % self.vocab_size
                        # end of sentence, or next word
                        # if word_id.item() in eos_token_ids or cur_len + 1 == max_len:
                        if (word_id.item() in eos_token_ids and cur_len + 1 <= self.max_len) or (
                                cur_len + 1 == self.max_len):
                            generated_hyps[batch_ex].add(
                                decoding_results[batch_ex * self.num_beams + beam_id, :cur_len].clone(), score.item()
                            )
                        else:
                            next_sent_beam.append((score, word_id, batch_ex * self.num_beams + beam_id))
                        # the beam for next step is full
                        if len(next_sent_beam) == self.num_beams:
                            break

                    # update next beam content
                    if cur_len + 1 == self.max_len:
                        assert len(next_sent_beam) == 0
                    else:
                        assert len(next_sent_beam) == self.num_beams

                    if len(next_sent_beam) == 0:
                        next_sent_beam = [(0, pad_id, 0)] * self.num_beams  # pad the batch
                    next_batch_beam.extend(next_sent_beam)
                    assert len(next_batch_beam) == self.num_beams * (batch_ex + 1)

                # sanity check / prepare next batch
                assert len(next_batch_beam) == batch_size * self.num_beams
                beam_scores = beam_scores.new([x[0] for x in next_batch_beam])
                beam_words = cur_input_ids.new([x[1] for x in next_batch_beam])
                beam_idx = cur_input_ids.new([x[2] for x in next_batch_beam])

                # re-order batch
                cur_input_ids = cur_input_ids[beam_idx, :]
                decoding_results = decoding_results[beam_idx, :]
                for module in incremental_state:
                    for key in incremental_state[module]:
                        result = incremental_state[module][key].index_select(0, beam_idx)
                        incremental_state[module][key] = result[:, :, :-1, :]

                next_ids = torch.full(
                    (batch_size * self.num_beams, 1), mask_id, dtype=torch.long, device=image.device
                )
                cur_input_ids = torch.cat([beam_words.unsqueeze(1), next_ids], dim=1)
                decoding_results[:, cur_len - 1] = beam_words
                # update current length
                cur_len = cur_len + 1
                # stop when we are done with each sentence
                if all(done):
                    break

            # select the best hypotheses
            tgt_len = torch.ones(batch_size, num_keep_best, dtype=torch.long)
            logprobs = torch.zeros(batch_size, num_keep_best,
                                   dtype=torch.float).fill_(-1e5).to(cur_input_ids.device)
            all_best = []

            for i, hypotheses in enumerate(generated_hyps):
                best = []
                hyp_scores = torch.tensor([x[0] for x in hypotheses.hyp])
                _, best_indices = torch.topk(hyp_scores,
                                             min(num_keep_best, len(hyp_scores)), largest=True)
                for best_idx, hyp_idx in enumerate(best_indices):
                    conf, best_hyp = hypotheses.hyp[hyp_idx]
                    best.append(best_hyp)
                    logprobs[i, best_idx] = conf
                    tgt_len[i, best_idx] = len(best_hyp) + 1  # +1 for the <EOS> symbol
                all_best.append(best)

            # generate target batch, pad to the same length
            decoded = cur_input_ids.new(batch_size, num_keep_best, self.max_len).fill_(pad_id)
            for batch_idx, best in enumerate(all_best):
                for best_idx, hypo in enumerate(best):
                    decoded[batch_idx, best_idx, : tgt_len[batch_idx, best_idx] - 1] = hypo
                    decoded[batch_idx, best_idx, tgt_len[batch_idx, best_idx] - 1] = eos_token_ids[0]

            captions = self.tokenizer.batch_decode(decoded.squeeze(1), skip_special_tokens=True)
            all_answer.append(remove_question_from_text(captions[0], question))
        return all_answer


def remove_question_from_text(full_text: str, question: str) -> str:
    if full_text.startswith(question):
        return full_text[len(question):].lstrip()  # 去掉前缀和多余空格
    else:
        return full_text  # 如果没匹配上，就不改

def load_checkpoint(model, url_or_filename_list, prefix='', ignore_missing="relative_position_index"):
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError('checkpoint url or path is invalid')
            state_dict = checkpoint['model']

    missing_keys = []
    unexpected_keys = []
    error_msgs = []
    # copy state_dict so _load_from_state_dict can modify it
    metadata = getattr(state_dict, '_metadata', None)
    state_dict = state_dict.copy()
    if metadata is not None:
        state_dict._metadata = metadata

    def load(module, prefix=''):
        local_metadata = {} if metadata is None else metadata.get(
            prefix[:-1], {})
        module._load_from_state_dict(
            state_dict, prefix, local_metadata, True, missing_keys, unexpected_keys, error_msgs)
        for name, child in module._modules.items():
            if child is not None:
                load(child, prefix + name + '.')

    load(model, prefix=prefix)

    warn_missing_keys = []
    ignore_missing_keys = []
    for key in missing_keys:
        keep_flag = True
        for ignore_key in ignore_missing.split('|'):
            if ignore_key in key:
                keep_flag = False
                break
        if keep_flag:
            warn_missing_keys.append(key)
        else:
            ignore_missing_keys.append(key)

    missing_keys = warn_missing_keys

    if len(missing_keys) > 0:
        print("Weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, missing_keys))
    if len(unexpected_keys) > 0:
        print("Weights from pretrained model not used in {}: {}".format(
            model.__class__.__name__, unexpected_keys))
    if len(ignore_missing_keys) > 0:
        print("Ignored weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, ignore_missing_keys))
    if len(error_msgs) > 0:
        print('\n'.join(error_msgs))
    print('load checkpoint from %s' % url_or_filename)

    return model

def is_url(url_or_filename):
    parsed = urlparse(url_or_filename)
    return parsed.scheme in ("http", "https")

def load_checkpoint_blip(model, url_or_filename_list, args=None):
    ############################################
    # IF UPDATING THIS FUNCTION, BLIP NLVR HAS
    # UNIQUE LOAD_CHECKPOINT THAT NEEDS TO BE
    # CHANGED AS WELL!
    ############################################
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if is_url(url_or_filename):
                cached_file = download_cached_file(url_or_filename, check_hash=False, progress=True)
                checkpoint = torch.load(cached_file, map_location='cpu')
            elif os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError(f'checkpoint url or path ({url_or_filename}) is invalid')

            state_dict = checkpoint['model']

            # state_dict['visual_encoder.pos_embed'] = interpolate_pos_embed(state_dict['visual_encoder.pos_embed'],
            #                                                                model.visual_encoder)
            if 'visual_encoder_m.pos_embed' in model.state_dict().keys() and 'visual_encoder_m.pos_embed' in state_dict.keys():
                state_dict['visual_encoder_m.pos_embed'] = interpolate_pos_embed(
                    state_dict['visual_encoder_m.pos_embed'],
                    model.visual_encoder_m)

            if isinstance(model.tokenizer, list):
                blip_w = state_dict['text_encoder.embeddings.word_embeddings.weight']
                if model.text_encoder.embeddings.word_embeddings.weight.shape != blip_w.shape:  # it may be that we are loading a model that already has the corrected embedding layer
                    toks_w = [(blip_w if x[-1] == 'blip' else x[1].word_embeddings.weight) for x in model.tokenizer]
                    new_weights = torch.cat(toks_w, dim=0).detach()
                    state_dict['text_encoder.embeddings.word_embeddings.weight'] = new_weights
                    state_dict['text_encoder_m.embeddings.word_embeddings.weight'] = new_weights

            if args is not None:
                if args['flush_queue']:
                    for key in list(state_dict.keys()):
                        if 'queue' in key:
                            print(f'Deleting {key} from checkpoint')
                            del state_dict[key]

            mdsd = model.state_dict()
            sdk = state_dict.keys()
            for key in mdsd.keys():
                if key in sdk:
                    if state_dict[key].shape != mdsd[key].shape:
                        del state_dict[key]
                elif 'lora_' in key:
                    # it could be that the model has a sequence of loras while the saved model has a single lora for the same
                    key_ = '.'.join(key.split('.')[:-1])
                    if ('lora_' in key_) and (
                            key_ in sdk):  # this means we stripped a number being the ModuleList index
                        state_dict[key] = state_dict[key_]
                        del state_dict[key_]

            if False:
                if url_or_filename != url_or_filename_list[0]:
                    diff_w = []
                    missing_w = []
                    for key in mdsd.keys():
                        if key in state_dict:
                            if not torch.all(mdsd[key].eq(state_dict[key])):
                                d = torch.max(torch.abs(mdsd[key] - state_dict[key]))
                                diff_w.append((key, d))
                        else:
                            missing_w.append(key)
                    # print(f'Missing: {missing_w}')
                    print(f'Different: {diff_w}')

            msg = model.load_state_dict(state_dict, strict=False)
            print('load checkpoint from %s' % url_or_filename)
    return model, msg