# --------------------------------------------------------
# Image as a Foreign Language: BEiT Pretraining for Vision and Vision-Language Tasks (https://arxiv.org/abs/2208.10442)
# Github source: https://github.com/microsoft/unilm/tree/master/beit3
# Copyright (c) 2023 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# --------------------------------------------------------'

import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.registry import register_model
import numpy as np
import os
import utils
from beit.modeling_utils import BEiT3Wrapper, _get_base_config, _get_large_config
from transformers import XLMRobertaTokenizer, BertTokenizer
from models.med import Bert<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BertLMHeadModel
from models.blip import create_vit, init_tokenizer
from urllib.parse import urlparse
from beit.glossary import normalize_word
def beit_nlvr(pretrained='', size='', **kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=384, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=384, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    model = BEiT3ForVisualReasoning(args, num_classes=2, **kwargs)
    head_not_loaded = True
    if pretrained:
        model = load_checkpoint(model, pretrained)
    return model, head_not_loaded


def beit_vqa(pretrained='', size='', task_id = -1, **kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=480, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=480, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    args.normalize_output = False
    model = BEiT3ForVisualQuestionAnswering(args, **kwargs)
    head_not_loaded = True
    # if pretrained:
    #     model = load_checkpoint(model, pretrained)
    if pretrained:
        if task_id != -1 and task_id == 0:
            # model,msg = load_checkpoint_blip(model, ['/root/xingxing/ckpt/model_base.pth'])
            model,msg = load_checkpoint_blip(model, ['/root/xingxing/ckpt/model_base_vqa_capfilt_large.pth'])
        model = load_checkpoint(model, pretrained)

    return model, head_not_loaded


class Pooler(nn.Module):
    def __init__(self, input_features, output_features, norm_layer):
        super().__init__()
        self.norm = norm_layer(input_features)
        self.dense = nn.Linear(input_features, output_features)
        self.activation = nn.Tanh()

    def forward(self, x):
        cls_rep = x[:, 0, :]
        cls_rep = self.norm(cls_rep)
        pooled_output = self.dense(cls_rep)
        pooled_output = self.activation(pooled_output)
        return pooled_output

class BEiT3ForVisualReasoning(BEiT3Wrapper):
    def __init__(
            self,
            args,
            num_classes,
            norm_layer=nn.LayerNorm,
            agent=None,
            **kwargs
    ):
        super(BEiT3ForVisualReasoning, self).__init__(args=args, agent=agent)
        embed_dim = args.encoder_embed_dim
        self.num_max_bpe_tokens = 64
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, num_classes)
        )
        self.pooler = Pooler(
            input_features=embed_dim,
            output_features=embed_dim,
            norm_layer=norm_layer,
        )
        self.pooler.apply(self._init_weights)
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            norm_layer(embed_dim * 2),
            nn.GELU(),
            nn.Linear(embed_dim * 2, num_classes),
        )
        self.cls_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def forward(self, image, text_description, targets, train=True, agent=None, feature_forward=False, train_zsl=False,
                wild_id=None, **kwargs):
        all_tokens = []
        all_token_ids = []
        all_language_tokens = []
        all_padding_masks = []

        for text in text_description:
            # 对每个文本进行分词
            tokens = self.tokenizer.tokenize(normalize_word(text))
            # 将分词结果转换为输入 ID
            token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
            # 获取文本片段、填充掩码等信息
            language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
            # 将结果存储到对应的列表中
            all_tokens.append(tokens)
            all_token_ids.append(token_ids)
            all_language_tokens.append(language_tokens)
            all_padding_masks.append(padding_mask)

        all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
        all_padding_masks = torch.tensor(all_padding_masks, dtype=torch.long).to(image.device)

        outputs = self.beit3(
            textual_tokens=all_language_tokens,
            visual_tokens=image,
            text_padding_position=all_padding_masks,
        )
        x = outputs["encoder_out"]
        cls_rep = self.pooler(x)
        prediction = self.cls_head(cls_rep)
        if train:
            if not train_zsl:
                loss = F.cross_entropy(prediction, targets)
            return loss
        else:
            return prediction

class BEiT3ForVisualQuestionAnswering(BEiT3Wrapper):
    def __init__(
            self,
            args,
            agent=None,
            **kwargs
    ):
        super(BEiT3ForVisualQuestionAnswering, self).__init__(args=args,agent=agent)
        embed_dim = args.encoder_embed_dim
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.num_max_bpe_tokens = 64
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.vocab_size = args.vocab_size
        # 添加MLM语言模型头替换原分类头
        self.mlm_head = nn.Linear(embed_dim, args.vocab_size)
        self.mlm_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def forward(self, image, question, answer=None, padding_mask=None,
                language_masked_pos=None, text_len=None, incremental_state=None,train=True, n=None, weights=None, agent=None, **kwargs):
        if incremental_state is None:
            if answer is not None:
                all_answer_ids = []
                answer_padding_ids = []
                for x, text in enumerate(answer):
                    # tokens = self.tokenizer.tokenize(normalize_word(text))
                    tokens = self.tokenizer.tokenize(text)
                    token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
                    language_tokens, answer_padding_mask, _ = self._get_text_segment(token_ids)
                    all_answer_ids.append(language_tokens)
                    answer_padding_ids.append(answer_padding_mask)
                answer_ids = torch.tensor(all_answer_ids, dtype=torch.long).to(image.device)
                answer_padding_mask = torch.tensor(answer_padding_ids, dtype=torch.long).to(image.device)

            all_language_tokens = []
            question_padding_ids = []
            for text in question:
                # 对每个文本进行分词
                tokens = self.tokenizer.tokenize(text)
                # 将分词结果转换为输入 ID
                token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
                # 获取文本片段、填充掩码等信息
                language_tokens, question_padding_mask, _ = self._get_text_segment(token_ids)
                # 将结果存储到对应的列表中
                all_language_tokens.append(language_tokens)
                question_padding_ids.append(question_padding_mask)

            all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
            question_padding_token = torch.tensor(question_padding_ids, dtype=torch.long).to(image.device)
            if train:
                question_all = []
                question_padding_all = []
                for b, i in enumerate(n):
                    question_all += [all_language_tokens[b]] * i
                    question_padding_all += [question_padding_token[b]] * i
                question = torch.stack(question_all, 0).to(image.device)
                question_padding_mask = torch.stack(question_padding_all, 0).to(image.device)
                # question_padding_mask = (question_padding == self.pad_token_id)

                image_all = []
                for b, i in enumerate(n):
                    image_all += [image[b]] * i
                image = torch.stack(image_all, 0).to(image.device)

                padding_mask = torch.cat([question_padding_mask, answer_padding_mask], dim=1)
        if train:
            # 如果提供了答案序列，则将问题和答案拼接作为完整文本输入
            if answer_ids is not None:
                # 拼接问题和答案token序列 (维度: batch_size x (Q_len + A_len))
                text_ids = torch.cat([question, answer_ids], dim=1)
            else:
                # 若没有提供答案（推理阶段第一次调用，只输入问题，可视为答案长度为0）
                text_ids = question
            # 计算各部分长度
            text_len_total = text_ids.size(1)
            question_len = question.size(1)
            image_len = self.beit3.vision_embed.num_position_embeddings()
            max_len = image_len + text_len_total  # 图像token数 + 文本token总数
            # 初始化注意力mask矩阵
            uni_mask = torch.zeros((max_len, max_len), dtype=torch.long, device=text_ids.device)
            # 索引定义
            context_start, context_end = 0, image_len + question_len    # 图像+问题作为context
            answer_start, answer_end = context_end, context_end + (text_len_total - question_len)  # 答案部分
            # 1) 图像和问题之间完全可见
            uni_mask[context_start:context_end, context_start:context_end] = 1
            # 2) 答案对自身使用下三角掩码（保证自回归）
            answer_length = answer_end - answer_start
            if answer_length > 0:
                uni_mask[answer_start:answer_end, answer_start:answer_end] = torch.tril(
                    torch.ones((answer_length, answer_length), dtype=torch.long, device=text_ids.device)
                )
            # 3) 答案可以关注图像和问题内容
            uni_mask[answer_start:answer_end, context_start:context_end] = 1
            # 取反得到实际的attention mask（1表示阻止关注，0表示允许）
            attn_mask = (1 - uni_mask).bool()
            # 如果使用增量生成，则初始化incremental_state对应的新token位置
            if incremental_state is not None:
                # 确保所有层都有初始状态字典
                for idx in range(self.get_num_layers()):
                    if idx not in incremental_state:
                        incremental_state[idx] = {}
                # 设定新增token的绝对位置索引 (起始于已有文本长度text_len)
                if text_len is None:
                    text_len = question_len  # 若未给出，则已有长度默认为question长度（首次生成）
                positions = torch.arange(text_len, text_len + (text_ids.size(1) - question_len),
                                          device=text_ids.device).long().unsqueeze(0)
            else:
                positions = None
            # 调用底层BEiT3模型得到融合图像和文本的隐藏表示
            outputs = self.beit3(
                textual_tokens=text_ids,
                visual_tokens=image,
                text_padding_position=padding_mask,
                attn_mask=attn_mask,
                incremental_state=incremental_state,
                positions=positions,
            )
            # 提取文本部分的输出特征（丢弃图像部分）
            encoder_out = outputs["encoder_out"]  # shape: [batch, max_len, embed_dim]
            # 文本部分开始索引为image_len（图像tokens在前）
            text_feat_start = image_len
            text_feats = encoder_out[:, text_feat_start:, :]  # 文本(问题+答案)的隐藏表示
            # 仅保留需要预测的答案位置的特征
            text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
            if language_masked_pos is not None:
                text_feats = text_feats[language_masked_pos.bool()]
            # 通过MLM头映射到词表空间，得到预测分布
            logits = self.mlm_head(text_feats)
            answer_start_idx = question.size(1)  # question 的长度
            answer_logits = logits[:, answer_start_idx:-1, :].contiguous()  # 只取答案部分的 logits
            shifted_labels = answer_ids[:, 1:].contiguous()  # 右移答案，作为目标

            loss_fct = nn.CrossEntropyLoss(reduction='none', label_smoothing=0.1, ignore_index=self.pad_token_id)
            lm_loss = loss_fct(answer_logits.view(-1, self.vocab_size), shifted_labels.view(-1))
            loss = weights * lm_loss.view(logits.size(0), -1).sum(1)
            loss = loss.sum() / image.size(0)
            return loss
        else:
            batch_size = image.size(0)
            answer_ids = torch.full((batch_size, 1), self.bos_token_id, dtype=torch.long, device=image.device)

            incremental_state = {}
            text_ids = torch.cat([all_language_tokens, answer_ids], dim=1)
            positions = None
            if incremental_state is not None:
                for idx in range(self.get_num_layers()):
                    if idx not in incremental_state:
                        incremental_state[idx] = {}
            outputs = self.beit3(
                textual_tokens=text_ids,
                visual_tokens=image,
                text_padding_position=None,
                attn_mask=None,
                incremental_state=incremental_state,
                positions=positions
            )

            encoder_out = outputs["encoder_out"]
            text_feats = encoder_out[:, -1:, :]  # 只取最新token特征
            text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
            logits = self.mlm_head(text_feats)

            next_token_ids = torch.argmax(logits[:, -1, :], dim=-1).unsqueeze(-1)
            answer_ids = torch.cat([answer_ids, next_token_ids], dim=1)

            for step in range(1, 10):
                cur_len = all_language_tokens.size(1) + answer_ids.size(1)  # 当前整体文本长度(问题+答案)
                text_ids = next_token_ids  # 增量输入，每次仅最新token
                positions = torch.arange(cur_len - 1, cur_len, device=image.device).unsqueeze(0)
                outputs = self.beit3(
                    textual_tokens=text_ids,
                    visual_tokens=None,  # 后续无图像输入
                    text_padding_position=None,
                    attn_mask=None,
                    incremental_state=incremental_state,
                    positions=positions
                )

                encoder_out = outputs["encoder_out"]
                text_feats = encoder_out[:, -1:, :]  # 每次最新token
                text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
                logits = self.mlm_head(text_feats)

                next_token_ids = torch.argmax(logits[:, -1, :], dim=-1).unsqueeze(-1)
                answer_ids = torch.cat([answer_ids, next_token_ids], dim=1)

                if torch.all(next_token_ids == self.eos_token_id):
                    break

            # 将生成的token转换为文本
            answers = []
            for ans_tokens in answer_ids:
                ans_tokens = ans_tokens.tolist()
                # 去掉开头的bos并且找到eos终止位置
                if self.eos_token_id in ans_tokens:
                    end_idx = ans_tokens.index(self.eos_token_id)
                    ans_tokens = ans_tokens[1:end_idx]
                else:
                    ans_tokens = ans_tokens[1:]
                answer_text = self.tokenizer.decode(ans_tokens)
                answers.append(answer_text)
            return answers


#
# class BEiT3ForVisualQuestionAnswering(BEiT3Wrapper):
#     def __init__(
#             self,
#             args,
#             norm_layer=nn.LayerNorm,
#             agent=None,
#             med_config='configs/med_config.json',
#             **kwargs
#     ):
#         super(BEiT3ForVisualQuestionAnswering, self).__init__(args=args, agent=agent)
#         embed_dim = args.encoder_embed_dim
#         self.tokenizer = init_tokenizer()
#         decoder_config = BertConfig.from_json_file(med_config)
#         self.text_decoder = BertLMHeadModel(config=decoder_config, agent=agent)
#         # self.pooler = Pooler(
#         #     input_features=embed_dim,
#         #     output_features=embed_dim,
#         #     norm_layer=norm_layer,
#         # )
#         # self.pooler.apply(self._init_weights)
#
#     def forward(self, image, question, answer=None, n=None, weights=None, train=True, inference='generate', k_test=128,
#                 train_zsl=False, agent=None, wild_id=None):
#         image_len = self.beit3.vision_embed.num_position_embeddings()
#         question = self.tokenizer(question, padding='longest', truncation=True, max_length=35,
#                                   return_tensors="pt").to(image.device)
#         question.input_ids[:, 0] = self.tokenizer.enc_token_id
#
#         if train:
#             outputs = self.beit3(
#                 textual_tokens=question.input_ids,
#                 visual_tokens=image,
#                 text_padding_position=None,
#             )
#             text_feats = outputs["encoder_out"][:, image_len:, :]
#             text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
#             # text_feats = outputs["encoder_out"][:, image_len:, :]
#             question_states = []
#             question_atts = []
#             for b, i in enumerate(n):
#                 question_states += [text_feats[b]] * i
#                 question_atts += [question.attention_mask[b]]*i
#             question_states = torch.stack(question_states, 0)
#             question_atts = torch.stack(question_atts, 0)
#
#             answer = self.tokenizer(answer, padding='longest', return_tensors="pt").to(image.device)
#             answer.input_ids[:, 0] = self.tokenizer.bos_token_id
#             answer_targets = answer.input_ids.masked_fill(answer.input_ids == self.tokenizer.pad_token_id, -100)
#
#             # outputs = self.beit3(
#             #                     textual_tokens=text_inputs,
#             #                     visual_tokens=image,
#             #                     text_padding_position=text_padding_mask,  # 文本padding位置
#             #                     attn_mask=attn_mask,
#             #                     incremental_state=None,  # 训练时不使用增量状态
#             #                 )
#
#             answer_output = self.text_decoder(answer.input_ids,
#                                               attention_mask=answer.attention_mask,
#                                               encoder_hidden_states=question_states,
#                                               encoder_attention_mask=question_atts,
#                                               labels=answer_targets,
#                                               return_dict=True,
#                                               reduction='none', wild_id=wild_id
#                                               )
#
#             loss = weights * answer_output.loss
#             loss = loss.sum() / image.size(0)
#             return loss
#         else:
#             outputs = self.beit3(
#                 textual_tokens=question.input_ids,
#                 visual_tokens=image,
#                 text_padding_position=None,
#             )
#             text_feats = outputs["encoder_out"][:, image_len:, :]
#             text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
#
#             if inference == 'generate':
#                 num_beams = 3
#                 question_states = text_feats.repeat_interleave(num_beams, dim=0)
#                 question_atts = torch.ones(question_states.size()[:-1], dtype=torch.long).to(question_states.device)
#                 model_kwargs = {"encoder_hidden_states": question_states, "encoder_attention_mask": question_atts}
#
#                 bos_ids = torch.full((image.size(0), 1), fill_value=self.tokenizer.bos_token_id, device=image.device)
#
#                 outputs = self.text_decoder.generate(input_ids=bos_ids,
#                                                      max_length=10,
#                                                      min_length=1,
#                                                      num_beams=num_beams,
#                                                      eos_token_id=self.tokenizer.sep_token_id,
#                                                      pad_token_id=self.tokenizer.pad_token_id,
#                                                      **model_kwargs)
#
#                 answers = []
#                 for output in outputs:
#                     answer = self.tokenizer.decode(output, skip_special_tokens=True)
#                     answers.append(answer)
#                 return answers
#


#
# class BEiT3ForVisualQuestionAnswering(BEiT3Wrapper):
#     def __init__(
#             self,
#             args,
#             num_classes,
#             norm_layer=nn.LayerNorm,
#             agent=None,
#             **kwargs
#     ):
#         super(BEiT3ForVisualQuestionAnswering, self).__init__(args=args, agent=agent)
#         embed_dim = args.encoder_embed_dim
#         self.num_max_bpe_tokens = 64
#         self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
#         self.bos_token_id = self.tokenizer.bos_token_id
#         self.eos_token_id = self.tokenizer.eos_token_id
#         self.pad_token_id = self.tokenizer.pad_token_id
#         self.cls_head = nn.Linear(embed_dim, num_classes)
#         self.cls_head.apply(self._init_weights)
#
#     def _get_text_segment(self, text_segment, max_len=None):
#         if isinstance(text_segment, str):
#             tokens = self.tokenizer.tokenize(text_segment)
#         else:
#             tokens = text_segment[:]
#         if len(tokens) == 0:
#             raise RuntimeError("The text segment should contains at least one tokens!")
#         if max_len is None:
#             max_len = self.num_max_bpe_tokens
#
#         if len(tokens) > max_len - 2:
#             tokens = tokens[:max_len - 2]
#
#         tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
#         num_tokens = len(tokens)
#         padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
#         return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens
#
#     def forward(self, image, question, answer=None, n=None, weights=None, train=True, inference='',
#                 incremental_state=None, train_zsl=False, agent=None, wild_id=None, **kwargs):
#         if train:
#             if incremental_state is None:
#                 all_answer_ids = []
#                 for x, text in enumerate(answer):
#                     tokens = self.tokenizer.tokenize(text)
#                     token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
#                     language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
#                     all_answer_ids.append(language_tokens)
#                 labels = torch.tensor(all_answer_ids, dtype=torch.long).to(image.device)
#
#                 all_tokens = []
#                 all_token_ids = []
#                 all_language_tokens = []
#
#                 for text in question:
#                     # 对每个文本进行分词
#                     tokens = self.tokenizer.tokenize(text)
#                     # 将分词结果转换为输入 ID
#                     token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
#                     # 获取文本片段、填充掩码等信息
#                     language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
#                     # 将结果存储到对应的列表中
#                     all_tokens.append(tokens)
#                     all_token_ids.append(token_ids)
#                     all_language_tokens.append(language_tokens)
#
#                 all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
#
#                 question_all = []
#                 for b, i in enumerate(n):
#                     question_all += [all_language_tokens[b]] * i
#                 question = torch.stack(question_all, 0)
#
#                 image_all = []
#                 for b, i in enumerate(n):
#                     image_all += [image[b]] * i
#                 image = torch.stack(image_all, 0).to(image.device)
#
#                 batch_size = question.size(0)
#                 ques_len = question.size(1)
#
#                 # answer = self.tokenizer(answer, padding='longest', return_tensors="pt").to(image.device)
#                 # answer.input_ids[:, 0] = self.tokenizer.bos_token_id
#                 # answer_targets = answer.input_ids.masked_fill(answer.input_ids == self.tokenizer.pad_token_id, -100)
#
#                 # 将padding标签替换为 -100 (忽略计算损失)
#                 labels = labels.masked_fill(labels == self.pad_token_id, -100)
#                 # 答案序列去除最后一个token（此应为<EOS>），并在前面加<BOS>
#                 bos_tokens = torch.full((batch_size, 1), self.bos_token_id, dtype=torch.long, device=image.device)
#                 ans_input = labels.clone()
#                 # labels 此时包含EOS和内容, 先取出答案序列（将-100暂视作pad处理，不影响前移）
#                 # 注意：labels的最后一个有效token应为EOS（非-100）,前移时将EOS移入序列内
#                 ans_without_pad = ans_input
#                 # 若 labels 包含 -100，需要将它们替换成 pad_token_id 再用于构建输入（因为-100非真实词表ID）
#                 ans_without_pad[ans_without_pad == -100] = self.pad_token_id
#                 answer_input_ids = torch.cat([bos_tokens, ans_without_pad[:, :-1]], dim=1)  # [BOS][答[:-1]]
#
#                 text_inputs = torch.cat([question, answer_input_ids], dim=1)
#                 ques_padding = (question == self.pad_token_id).long()  # 1表示PAD
#                 ans_padding = (answer_input_ids == self.pad_token_id).long()
#                 text_padding_mask = torch.cat([ques_padding, ans_padding], dim=1)  # [batch, total_text_len]
#                 # 构造注意力mask (uni_mask): 允许的注意力位置标记为1，稍后取反得到attn_mask
#                 total_text_len = text_inputs.size(1)
#                 # 图像token长度
#                 image_len = self.beit3.vision_embed.num_position_embeddings()  # 获取图像序列长度
#                 total_len = image_len + total_text_len
#                 # 初始化全零的允许矩阵
#                 uni_mask = torch.zeros((total_len, total_len), dtype=torch.long, device=image.device)
#                 # 定义各分段索引
#                 img_start, img_end = 0, image_len
#                 ques_start, ques_end = image_len, image_len + ques_len
#                 ans_start, ans_end = image_len + ques_len, total_len  # ans_end 索引在总序列上为 total_len-1 (切片用total_len包含)
#                 # 1) 图像->图像 允许
#                 uni_mask[img_start:img_end, img_start:img_end] = 1
#                 # 2) 问题->问题 允许
#                 uni_mask[ques_start:ques_end, ques_start:ques_end] = 1
#                 # 3) 图像<->问题 允许 (双向都允许融合)
#                 uni_mask[img_start:img_end, ques_start:ques_end] = 1
#                 uni_mask[ques_start:ques_end, img_start:img_end] = 1
#                 # 4) 答案->答案 只允许看见自己和之前（下三角，包括对角线）
#                 ans_len = ans_end - ans_start  # 答案输入总长度 (包含<BOS>和可能的<EOS>)
#                 uni_mask[ans_start:ans_end, ans_start:ans_end] = torch.tril(
#                     torch.ones((ans_len, ans_len), dtype=torch.long, device=image.device))
#                 # 5) 答案->图像 允许
#                 uni_mask[ans_start:ans_end, img_start:img_end] = 1
#                 # 6) 答案->问题 允许
#                 uni_mask[ans_start:ans_end, ques_start:ques_end] = 1
#                 # 7) 上下文->答案 不允许 (保持为0即可，不用显式设置)
#                 # 最后，翻转mask：1变0，0变1，得到attn_mask，其中1表示禁止关注的位置
#                 attn_mask = 1 - uni_mask
#
#             outputs = self.beit3(
#                 textual_tokens=text_inputs,
#                 visual_tokens=image,
#                 text_padding_position=text_padding_mask,  # 文本padding位置
#                 attn_mask=attn_mask,
#                 incremental_state=None,  # 训练时不使用增量状态
#             )
#             text_feats = outputs["encoder_out"][:, image_len:, :]
#             # 通过 mlm_head 得到每个位置的词表分布 logits
#             # prediction = self.cls_head(self.pooler(text_feats))
#             text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
#             prediction = self.cls_head(text_feats)
#
#             labels = labels.to(prediction.device)
#             # **Shifted Prediction**: 对齐预测和标签，将预测右移一位与下一个词标签对齐
#             # shifted_prediction_scores = prediction[:, :-1, :].contiguous()  # 去除最后一个时间步的预测
#             # shifted_labels = labels[:, 1:].contiguous()  # 去除标签序列开头的<BOS>（或问题末尾）
#             # 配置损失函数
#             # 确保 prediction_scores 和 labels 具有相同的时间步
#             min_seq_len = min(prediction.shape[1] - 1, labels.shape[1] - 1)
#             # 修正 shifted_prediction_scores 和 shifted_labels 的形状
#             shifted_prediction_scores = prediction[:, :min_seq_len, :].contiguous()  # 取前 min_seq_len 个 token
#             shifted_labels = labels[:, 1:min_seq_len + 1].contiguous()  # 右移对齐 labels，确保长度一致
#             # 计算损失
#             loss_fct = nn.CrossEntropyLoss(reduction='none', label_smoothing=0.1)
#             # 将 (batch, seq_len-1, vocab) 展平为 (batch*(seq_len-1), vocab)，标签展平为相应长度
#             loss = loss_fct(shifted_prediction_scores.view(-1, shifted_prediction_scores.size(-1)),
#                             shifted_labels.view(-1))
#             loss = weights * loss.view(prediction.size(0), -1).sum(1)
#             loss = loss.sum() / image.size(0)
#             return loss
#         else:
#             return self.generate(image, question, n, inference= inference)
#
#     def generate(self, image, question, n, num_beams=1, max_length=10, incremental_state=None,inference = ''):
#         image_len = self.beit3.vision_embed.num_position_embeddings()
#         if incremental_state == None and inference == 'generate':
#             self.eval()  # 推理模式
#             all_tokens = []
#             all_token_ids = []
#             all_language_tokens = []
#
#             for text in question:
#                 # 对每个文本进行分词
#                 tokens = self.tokenizer.tokenize(text)
#                 # 将分词结果转换为输入 ID
#                 token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
#                 # 获取文本片段、填充掩码等信息
#                 language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
#                 # 将结果存储到对应的列表中
#                 all_tokens.append(tokens)
#                 all_token_ids.append(token_ids)
#                 all_language_tokens.append(language_tokens)
#
#             question = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
#
#             # question_all = []
#             # for b, i in enumerate(n):
#             #     question_all += [all_language_tokens[b]] * i
#             # question = torch.stack(question_all, 0)
#             #
#             # image_all = []
#             # for b, i in enumerate(n):
#             #     image_all += [image[b]] * i
#             # image = torch.stack(image_all, 0).to(image.device)
#
#         """
#         使用Beam Search生成答案序列。返回每个batch输入对应生成的答案token序列（不含<BOS>和<EOS>）。
#         """
#         batch_size = question.size(0)
#         ques_len = question.size(1)
#
#         # 初始化增量状态字典（各层缓存）
#         if incremental_state is None:
#             incremental_state = {}
#
#         # 准备初始输入：在问题后附加<BOS>作为答案起始
#         bos_tokens = torch.full((batch_size, 1), self.bos_token_id, dtype=torch.long, device=question.device)
#         # 假定问题 `question` 结尾已包含 `</s>`，此处直接拼接 `<BOS>`
#         current_sequences = torch.cat([question, bos_tokens], dim=1)  # (batch_size, seq_len+1)
#
#
#         # 初始化 beam search
#         beams = [{"tokens": current_sequences[i:i + 1], "log_prob": 0.0, "incremental_state": {}} for i in
#                  range(batch_size)]
#         for beam in beams:
#             beam["incremental_state"] = {idx: {} for idx in
#                                          range(self.get_num_layers())}  # 这里初始化所有层的 `incremental_state`
#         # 生成循环
#         generated = None
#         for step in range(max_length):
#             all_candidates = []
#             for beam in beams:
#                 seq_tensor = beam["tokens"]  # shape [1, seq_len]
#
#                 # **确保 batch 维度匹配 image**
#                 current_batch_size = seq_tensor.shape[0]
#                 if current_batch_size != batch_size:
#                     seq_tensor = seq_tensor.expand(batch_size, -1)  # 扩展 batch 维度
#
#                 # **若已经生成 EOS，则跳过扩展**
#                 if int(seq_tensor[0, -1]) == self.eos_token_id:
#                     all_candidates.append(beam)
#                     continue
#
#                 # **构造文本 padding mask**
#                 text_padding_mask = (seq_tensor == self.pad_token_id).long()
#
#                 # **调用 self.beit3()**
#                 outputs = self.beit3(
#                     textual_tokens=seq_tensor,
#                     visual_tokens=image,
#                     text_padding_position=text_padding_mask,
#                     attn_mask=None,  # 推理时无需掩码
#                     incremental_state=beam["incremental_state"],  # 增量状态
#                 )
#
#                 # **提取文本特征**
#                 text_feats = outputs["encoder_out"][:, image_len:, :]
#                 text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])  # 归一化
#                 prediction = self.cls_head(text_feats)  # 预测 token logits
#
#                 # **取最后一个时间步的 logits**
#                 logits = prediction[:, -1, :]  # shape [batch_size, vocab_size]
#                 probs = F.log_softmax(logits, dim=-1)  # 对数概率
#                 topk_probs, topk_indices = torch.topk(probs, num_beams)
#
#                 for k in range(num_beams):
#                     new_token_id = topk_indices[:, k].unsqueeze(1)  # shape [batch_size, 1]
#                     new_log_prob = beam["log_prob"] + topk_probs[:, k]
#                     new_seq = torch.cat([seq_tensor, new_token_id], dim=1)  # shape [batch_size, seq_len+1]
#
#                     # **确保 incremental_state 按 batch 维度更新**
#                     # beam["incremental_state"] = {layer: state.expand(batch_size, *state.shape[1:]) for layer, state in
#                                                  # beam["incremental_state"].items()}
#                     beam["incremental_state"] = {
#                         idx: state.copy() if idx in beam["incremental_state"] else {} for idx, state in
#                         beam["incremental_state"].items()
#                     }
#                     all_candidates.append({
#                         "tokens": new_seq,
#                         "log_prob": new_log_prob,
#                         "incremental_state": beam["incremental_state"]
#                     })
#
#             # **从所有候选中选取 top num_beams**
#             # all_candidates.sort(key=lambda x: x["log_prob"], reverse=True)
#             all_candidates.sort(key=lambda x: x["log_prob"].cpu().numpy().tolist(), reverse=True)
#
#             beams = all_candidates[:num_beams]
#
#             # **检查是否有序列完成**
#             finished_beams = [beam for beam in beams if int(beam["tokens"][0, -1]) == self.eos_token_id]
#             if finished_beams:
#                 best_beam = max(finished_beams, key=lambda x: x["log_prob"])
#                 generated = best_beam["tokens"]
#                 break
#
#         # **如果循环完仍无 EOS，则取最高分数的 beam**
#         if generated is None:
#             best_beam = max(beams, key=lambda x: x["log_prob"])
#             generated = best_beam["tokens"]
#
#         # **移除问题和<BOS>**
#         gen_tokens = generated[:, min(ques_len + 1, generated.shape[1]):]  # 跳过问题和<BOS>
#         mask = gen_tokens[:, -1] == self.eos_token_id  # 找出 <EOS> 位置
#         gen_tokens[mask, -1] = self.pad_token_id  # 用 PAD 替换 EOS
#
#         # **解码生成的 tokens**
#         answers = [self.tokenizer.decode(tokens, skip_special_tokens=True) for tokens in gen_tokens]
#         return answers



def load_checkpoint(model, url_or_filename_list, prefix='', ignore_missing="relative_position_index"):
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError('checkpoint url or path is invalid')
            state_dict = checkpoint['model']

    missing_keys = []
    unexpected_keys = []
    error_msgs = []
    # copy state_dict so _load_from_state_dict can modify it
    metadata = getattr(state_dict, '_metadata', None)
    state_dict = state_dict.copy()
    if metadata is not None:
        state_dict._metadata = metadata

    def load(module, prefix=''):
        local_metadata = {} if metadata is None else metadata.get(
            prefix[:-1], {})
        module._load_from_state_dict(
            state_dict, prefix, local_metadata, True, missing_keys, unexpected_keys, error_msgs)
        for name, child in module._modules.items():
            if child is not None:
                load(child, prefix + name + '.')

    load(model, prefix=prefix)

    warn_missing_keys = []
    ignore_missing_keys = []
    for key in missing_keys:
        keep_flag = True
        for ignore_key in ignore_missing.split('|'):
            if ignore_key in key:
                keep_flag = False
                break
        if keep_flag:
            warn_missing_keys.append(key)
        else:
            ignore_missing_keys.append(key)

    missing_keys = warn_missing_keys

    if len(missing_keys) > 0:
        print("Weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, missing_keys))
    if len(unexpected_keys) > 0:
        print("Weights from pretrained model not used in {}: {}".format(
            model.__class__.__name__, unexpected_keys))
    if len(ignore_missing_keys) > 0:
        print("Ignored weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, ignore_missing_keys))
    if len(error_msgs) > 0:
        print('\n'.join(error_msgs))

    return model

def is_url(url_or_filename):
    parsed = urlparse(url_or_filename)
    return parsed.scheme in ("http", "https")

def load_checkpoint_blip(model, url_or_filename_list, args=None):
    ############################################
    # IF UPDATING THIS FUNCTION, BLIP NLVR HAS
    # UNIQUE LOAD_CHECKPOINT THAT NEEDS TO BE
    # CHANGED AS WELL!
    ############################################
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if is_url(url_or_filename):
                cached_file = download_cached_file(url_or_filename, check_hash=False, progress=True)
                checkpoint = torch.load(cached_file, map_location='cpu')
            elif os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError(f'checkpoint url or path ({url_or_filename}) is invalid')

            state_dict = checkpoint['model']

            # state_dict['visual_encoder.pos_embed'] = interpolate_pos_embed(state_dict['visual_encoder.pos_embed'],
            #                                                                model.visual_encoder)
            if 'visual_encoder_m.pos_embed' in model.state_dict().keys() and 'visual_encoder_m.pos_embed' in state_dict.keys():
                state_dict['visual_encoder_m.pos_embed'] = interpolate_pos_embed(
                    state_dict['visual_encoder_m.pos_embed'],
                    model.visual_encoder_m)

            if isinstance(model.tokenizer, list):
                blip_w = state_dict['text_encoder.embeddings.word_embeddings.weight']
                if model.text_encoder.embeddings.word_embeddings.weight.shape != blip_w.shape:  # it may be that we are loading a model that already has the corrected embedding layer
                    toks_w = [(blip_w if x[-1] == 'blip' else x[1].word_embeddings.weight) for x in model.tokenizer]
                    new_weights = torch.cat(toks_w, dim=0).detach()
                    state_dict['text_encoder.embeddings.word_embeddings.weight'] = new_weights
                    state_dict['text_encoder_m.embeddings.word_embeddings.weight'] = new_weights

            if args is not None:
                if args['flush_queue']:
                    for key in list(state_dict.keys()):
                        if 'queue' in key:
                            print(f'Deleting {key} from checkpoint')
                            del state_dict[key]

            mdsd = model.state_dict()
            sdk = state_dict.keys()
            for key in mdsd.keys():
                if key in sdk:
                    if state_dict[key].shape != mdsd[key].shape:
                        del state_dict[key]
                elif 'lora_' in key:
                    # it could be that the model has a sequence of loras while the saved model has a single lora for the same
                    key_ = '.'.join(key.split('.')[:-1])
                    if ('lora_' in key_) and (
                            key_ in sdk):  # this means we stripped a number being the ModuleList index
                        state_dict[key] = state_dict[key_]
                        del state_dict[key_]

            if False:
                if url_or_filename != url_or_filename_list[0]:
                    diff_w = []
                    missing_w = []
                    for key in mdsd.keys():
                        if key in state_dict:
                            if not torch.all(mdsd[key].eq(state_dict[key])):
                                d = torch.max(torch.abs(mdsd[key] - state_dict[key]))
                                diff_w.append((key, d))
                        else:
                            missing_w.append(key)
                    # print(f'Missing: {missing_w}')
                    print(f'Different: {diff_w}')

            msg = model.load_state_dict(state_dict, strict=False)
            print('load checkpoint from %s' % url_or_filename)
    return model, msg