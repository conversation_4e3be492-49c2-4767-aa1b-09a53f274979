# --------------------------------------------------------
# Image as a Foreign Language: BEiT Pretraining for Vision and Vision-Language Tasks (https://arxiv.org/abs/2208.10442)
# Github source: https://github.com/microsoft/unilm/tree/master/beit3
# Copyright (c) 2023 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# --------------------------------------------------------'
from torch.nn.utils.rnn import pad_sequence
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.registry import register_model
import numpy as np
import os
# import utils
from beit.modeling_utils import BEiT3Wrapper, _get_base_config, _get_large_config
from transformers import XLMRobertaTokenizer, BertTokenizer
from models.med import Bert<PERSON><PERSON>fi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Model
from models.blip import create_vit, init_tokenizer
from urllib.parse import urlparse
from beit.glossary import normalize_word
from beit.utils import BeamHypotheses
def beit_nlvr(pretrained='', size='', **kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=384, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=384, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    model = BEiT3ForVisualReasoning(args, num_classes=2, **kwargs)
    head_not_loaded = True
    if pretrained:
        model = load_checkpoint(model, pretrained)
    return model, head_not_loaded


def beit_vqa(pretrained='', size='', task_id = -1, **kwargs):
    if 'base' in size:
        args = _get_base_config(img_size=480, **kwargs)
    elif 'large' in size:
        args = _get_large_config(img_size=480, **kwargs)
    else:
        assert False, 'Unknown pretrained model'
    args.normalize_output = False
    model = BEiT3ForVisualQuestionAnswering(args, **kwargs)
    head_not_loaded = True
    # if pretrained:
    #     model = load_checkpoint(model, pretrained)
    if pretrained:
        if task_id != -1 and task_id == 0:
            model,msg = load_checkpoint_blip(model, ['/root/xingxing/ckpt/model_large.pth'])
        model = load_checkpoint(model, pretrained)

    return model, head_not_loaded



class Pooler(nn.Module):
    def __init__(self, input_features, output_features, norm_layer):
        super().__init__()
        self.norm = norm_layer(input_features)
        self.dense = nn.Linear(input_features, output_features)
        self.activation = nn.Tanh()

    def forward(self, x):
        cls_rep = x[:, 0, :]
        cls_rep = self.norm(cls_rep)
        pooled_output = self.dense(cls_rep)
        pooled_output = self.activation(pooled_output)
        return pooled_output

class BEiT3ForVisualReasoning(BEiT3Wrapper):
    def __init__(
            self,
            args,
            num_classes,
            norm_layer=nn.LayerNorm,
            agent=None,
            **kwargs
    ):
        super(BEiT3ForVisualReasoning, self).__init__(args=args, agent=agent)
        embed_dim = args.encoder_embed_dim
        self.num_max_bpe_tokens = 64
        self.tokenizer = XLMRobertaTokenizer('beit/beit3.spm')
        self.bos_token_id = self.tokenizer.bos_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.pad_token_id = self.tokenizer.pad_token_id
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, num_classes)
        )
        self.pooler = Pooler(
            input_features=embed_dim,
            output_features=embed_dim,
            norm_layer=norm_layer,
        )
        self.pooler.apply(self._init_weights)
        self.cls_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            norm_layer(embed_dim * 2),
            nn.GELU(),
            nn.Linear(embed_dim * 2, num_classes),
        )
        self.cls_head.apply(self._init_weights)

    def _get_text_segment(self, text_segment, max_len=None):
        if isinstance(text_segment, str):
            tokens = self.tokenizer.tokenize(text_segment)
        else:
            tokens = text_segment[:]
        if len(tokens) == 0:
            raise RuntimeError("The text segment should contains at least one tokens!")
        if max_len is None:
            max_len = self.num_max_bpe_tokens

        if len(tokens) > max_len - 2:
            tokens = tokens[:max_len - 2]

        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]
        num_tokens = len(tokens)
        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)
        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens

    def forward(self, image, text_description, targets, train=True, agent=None, feature_forward=False, train_zsl=False,
                wild_id=None, **kwargs):
        all_tokens = []
        all_token_ids = []
        all_language_tokens = []
        all_padding_masks = []

        for text in text_description:
            # 对每个文本进行分词
            tokens = self.tokenizer.tokenize(normalize_word(text))
            # 将分词结果转换为输入 ID
            token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
            # 获取文本片段、填充掩码等信息
            language_tokens, padding_mask, _ = self._get_text_segment(token_ids)
            # 将结果存储到对应的列表中
            all_tokens.append(tokens)
            all_token_ids.append(token_ids)
            all_language_tokens.append(language_tokens)
            all_padding_masks.append(padding_mask)

        all_language_tokens = torch.tensor(all_language_tokens, dtype=torch.long).to(image.device)
        all_padding_masks = torch.tensor(all_padding_masks, dtype=torch.long).to(image.device)

        outputs = self.beit3(
            textual_tokens=all_language_tokens,
            visual_tokens=image,
            text_padding_position=all_padding_masks,
        )
        x = outputs["encoder_out"]
        cls_rep = self.pooler(x)
        prediction = self.cls_head(cls_rep)
        if train:
            if not train_zsl:
                loss = F.cross_entropy(prediction, targets)
            return loss
        else:
            return prediction

class BEiT3ForVisualQuestionAnswering(BEiT3Wrapper):
    def __init__(
            self,
            args,
            norm_layer=nn.LayerNorm,
            agent=None,
            med_config='configs/med_config.json',
            **kwargs
    ):
        super(BEiT3ForVisualQuestionAnswering, self).__init__(args=args, agent=agent)
        embed_dim = args.encoder_embed_dim
        self.tokenizer = init_tokenizer()
        decoder_config = BertConfig.from_json_file(med_config)
        self.text_decoder = BertLMHeadModel(config=decoder_config, agent=agent)

    def forward(self, image, question, answer=None, n=None, weights=None, train=True, inference='generate', k_test=128,
                train_zsl=False, agent=None, wild_id=None):
        image_len = self.beit3.vision_embed.num_position_embeddings()
        question = self.tokenizer(question, padding='longest', truncation=True, max_length=35,
                                  return_tensors="pt").to(image.device)
        question.input_ids[:, 0] = self.tokenizer.enc_token_id

        if train:
            outputs = self.beit3(
                textual_tokens=question.input_ids,
                visual_tokens=image,
                text_padding_position=None,
            )
            text_feats = outputs["encoder_out"][:, image_len:, :]
            text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])
            # text_feats = outputs["encoder_out"][:, image_len:, :]
            question_states = []
            question_atts = []
            for b, i in enumerate(n):
                question_states += [text_feats[b]] * i
                question_atts += [question.attention_mask[b]]*i
            question_states = torch.stack(question_states, 0)
            question_atts = torch.stack(question_atts, 0)

            answer = self.tokenizer(answer, padding='longest', return_tensors="pt").to(image.device)
            answer.input_ids[:, 0] = self.tokenizer.bos_token_id
            answer_targets = answer.input_ids.masked_fill(answer.input_ids == self.tokenizer.pad_token_id, -100)

            answer_output = self.text_decoder(answer.input_ids,
                                              attention_mask=answer.attention_mask,
                                              encoder_hidden_states=question_states,
                                              encoder_attention_mask=question_atts,
                                              labels=answer_targets,
                                              return_dict=True,
                                              reduction='none', wild_id=wild_id
                                              )

            loss = weights * answer_output.loss
            loss = loss.sum() / image.size(0)
            return loss
        else:
            outputs = self.beit3(
                textual_tokens=question.input_ids,
                visual_tokens=image,
                text_padding_position=None,
            )
            text_feats = outputs["encoder_out"][:, image_len:, :]
            text_feats = F.layer_norm(text_feats, text_feats.shape[-1:])

            if inference == 'generate':
                num_beams = 3
                question_states = text_feats.repeat_interleave(num_beams, dim=0)
                question_atts = torch.ones(question_states.size()[:-1], dtype=torch.long).to(question_states.device)
                model_kwargs = {"encoder_hidden_states": question_states, "encoder_attention_mask": question_atts}

                bos_ids = torch.full((image.size(0), 1), fill_value=self.tokenizer.bos_token_id, device=image.device)

                outputs = self.text_decoder.generate(input_ids=bos_ids,
                                                     max_length=10,
                                                     min_length=1,
                                                     num_beams=num_beams,
                                                     eos_token_id=self.tokenizer.sep_token_id,
                                                     pad_token_id=self.tokenizer.pad_token_id,
                                                     **model_kwargs)

                answers = []
                for output in outputs:
                    answer = self.tokenizer.decode(output, skip_special_tokens=True)
                    answers.append(answer)
                return answers



def remove_question_from_text(full_text: str, question: str) -> str:
    if full_text.startswith(question):
        return full_text[len(question):].lstrip()  # 去掉前缀和多余空格
    else:
        return full_text  # 如果没匹配上，就不改


def load_checkpoint(model, url_or_filename_list, prefix='', ignore_missing="relative_position_index"):
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError('checkpoint url or path is invalid')
            state_dict = checkpoint['model']

    missing_keys = []
    unexpected_keys = []
    error_msgs = []
    # copy state_dict so _load_from_state_dict can modify it
    metadata = getattr(state_dict, '_metadata', None)
    state_dict = state_dict.copy()
    if metadata is not None:
        state_dict._metadata = metadata

    def load(module, prefix=''):
        local_metadata = {} if metadata is None else metadata.get(
            prefix[:-1], {})
        module._load_from_state_dict(
            state_dict, prefix, local_metadata, True, missing_keys, unexpected_keys, error_msgs)
        for name, child in module._modules.items():
            if child is not None:
                load(child, prefix + name + '.')

    load(model, prefix=prefix)

    warn_missing_keys = []
    ignore_missing_keys = []
    for key in missing_keys:
        keep_flag = True
        for ignore_key in ignore_missing.split('|'):
            if ignore_key in key:
                keep_flag = False
                break
        if keep_flag:
            warn_missing_keys.append(key)
        else:
            ignore_missing_keys.append(key)

    missing_keys = warn_missing_keys

    if len(missing_keys) > 0:
        print("Weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, missing_keys))
    if len(unexpected_keys) > 0:
        print("Weights from pretrained model not used in {}: {}".format(
            model.__class__.__name__, unexpected_keys))
    if len(ignore_missing_keys) > 0:
        print("Ignored weights of {} not initialized from pretrained model: {}".format(
            model.__class__.__name__, ignore_missing_keys))
    if len(error_msgs) > 0:
        print('\n'.join(error_msgs))
    print('load checkpoint from %s' % url_or_filename)

    return model

def is_url(url_or_filename):
    parsed = urlparse(url_or_filename)
    return parsed.scheme in ("http", "https")

def load_checkpoint_blip(model, url_or_filename_list, args=None):
    ############################################
    # IF UPDATING THIS FUNCTION, BLIP NLVR HAS
    # UNIQUE LOAD_CHECKPOINT THAT NEEDS TO BE
    # CHANGED AS WELL!
    ############################################
    if not isinstance(url_or_filename_list, list):
        url_or_filename_list = [url_or_filename_list]

    for url_or_filename in url_or_filename_list:
        if url_or_filename is not None and url_or_filename != 'None':
            if is_url(url_or_filename):
                cached_file = download_cached_file(url_or_filename, check_hash=False, progress=True)
                checkpoint = torch.load(cached_file, map_location='cpu')
            elif os.path.isfile(url_or_filename):
                checkpoint = torch.load(url_or_filename, map_location='cpu')
            else:
                raise RuntimeError(f'checkpoint url or path ({url_or_filename}) is invalid')

            state_dict = checkpoint['model']

            # state_dict['visual_encoder.pos_embed'] = interpolate_pos_embed(state_dict['visual_encoder.pos_embed'],
            #                                                                model.visual_encoder)
            if 'visual_encoder_m.pos_embed' in model.state_dict().keys() and 'visual_encoder_m.pos_embed' in state_dict.keys():
                state_dict['visual_encoder_m.pos_embed'] = interpolate_pos_embed(
                    state_dict['visual_encoder_m.pos_embed'],
                    model.visual_encoder_m)

            if isinstance(model.tokenizer, list):
                blip_w = state_dict['text_encoder.embeddings.word_embeddings.weight']
                if model.text_encoder.embeddings.word_embeddings.weight.shape != blip_w.shape:  # it may be that we are loading a model that already has the corrected embedding layer
                    toks_w = [(blip_w if x[-1] == 'blip' else x[1].word_embeddings.weight) for x in model.tokenizer]
                    new_weights = torch.cat(toks_w, dim=0).detach()
                    state_dict['text_encoder.embeddings.word_embeddings.weight'] = new_weights
                    state_dict['text_encoder_m.embeddings.word_embeddings.weight'] = new_weights

            if args is not None:
                if args['flush_queue']:
                    for key in list(state_dict.keys()):
                        if 'queue' in key:
                            print(f'Deleting {key} from checkpoint')
                            del state_dict[key]

            mdsd = model.state_dict()
            sdk = state_dict.keys()
            for key in mdsd.keys():
                if key in sdk:
                    if state_dict[key].shape != mdsd[key].shape:
                        del state_dict[key]
                elif 'lora_' in key:
                    # it could be that the model has a sequence of loras while the saved model has a single lora for the same
                    key_ = '.'.join(key.split('.')[:-1])
                    if ('lora_' in key_) and (
                            key_ in sdk):  # this means we stripped a number being the ModuleList index
                        state_dict[key] = state_dict[key_]
                        del state_dict[key_]

            if False:
                if url_or_filename != url_or_filename_list[0]:
                    diff_w = []
                    missing_w = []
                    for key in mdsd.keys():
                        if key in state_dict:
                            if not torch.all(mdsd[key].eq(state_dict[key])):
                                d = torch.max(torch.abs(mdsd[key] - state_dict[key]))
                                diff_w.append((key, d))
                        else:
                            missing_w.append(key)
                    # print(f'Missing: {missing_w}')
                    print(f'Different: {diff_w}')

            msg = model.load_state_dict(state_dict, strict=False)
            print('load checkpoint from %s' % url_or_filename)
    return model, msg