export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export HF_ENDPOINT=https://hf-mirror.com
# export http_proxy=http://10.0.0.100:7890
# export https_proxy=http://10.0.0.100:7890
# export HTTP_PROXY=http://10.0.0.100:7890
# export HTTPS_PROXY=http://10.0.0.100:7890

export PATH="/usr/local/cuda/bin:$PATH"
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"

# export PATH="/usr/local/cuda-11.8/bin:$PATH"
# export LD_LIBRARY_PATH="/usr/local/cuda-11.8/lib64:$LD_LIBRARY_PATH"

#cd /root/xingxing/project/nvlr_vqa_multi_wild/

#torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/2task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_0.85/  --zsl_config ./configs/new_wild/2zero_shot_vqa.yaml
#
#torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/3task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_0.85/  --zsl_config ./configs/new_wild/3zero_shot_vqa.yaml
##
#torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/4task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_0.85/  --zsl_config ./configs/new_wild/4zero_shot_vqa.yaml
##
#torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/5task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_0.85/  --zsl_config ./configs/new_wild/5zero_shot_vqa.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 /data1/home/<USER>/EQA/qa/ours_baseline/run_me.py --config /data1/home/<USER>/EQA/qa/configs/vqa_task_order/10task_vqa_checklist_m2l.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /data1/home/<USER>/EQA/qa/outputs/10task_MultiLoRa --zsl_config /data1/home/<USER>/EQA/qa/configs/vqa_task_order/zero_shot_gqa.yaml
##
# torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/7task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_0.85/  --zsl_config ./configs/new_wild/7zero_shot_vqa.yaml

# ##
# torchrun --nproc_per_node=3  --master_port=25331 run_me.py --config ./configs/new_wild/7task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name LoRa --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_ER_memory_50/ --memory 50 --zsl_config ./configs/new_wild/2zero_shot_vqa.yaml






# #torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/2task_vqa_base+cap.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_base+cap_0.85/  --zsl_config ./configs/new_wild/2zero_shot_vqa.yaml
# #
# #torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/3task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_base+cap_0.85/  --zsl_config ./configs/new_wild/3zero_shot_vqa.yaml
# ##
# #torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/4task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_base+cap_0.85/  --zsl_config ./configs/new_wild/4zero_shot_vqa.yaml
# ##
# #torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/5task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_base+cap_0.85/  --zsl_config ./configs/new_wild/5zero_shot_vqa.yaml
# ##
# torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/6task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_base+cap_0.85/  --zsl_config ./configs/new_wild/6zero_shot_vqa.yaml
# ##
# torchrun --nproc_per_node=6  --master_port=25334 run_me.py --config ./configs/new_wild/7task_vqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_vqa/7task_MultiLoRa_wild_id_base+cap_0.85/  --zsl_config ./configs/new_wild/7zero_shot_vqa.yaml