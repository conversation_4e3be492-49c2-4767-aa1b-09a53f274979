"""
持续学习训练脚本 - 详细伪代码

=== 主要功能概述 ===
本脚本实现了一个多任务持续学习训练框架，支持顺序学习多个视觉语言任务

=== 详细伪代码流程 ===

1. 初始化阶段:
   输入: 配置文件, 命令行参数
   BEGIN 初始化
       解析命令行参数 (任务配置, 输出目录, 代理类型等)
       设置随机种子确保可重复性
       初始化分布式训练环境
       加载任务序列配置和零样本配置
       创建持续学习代理(agent) 根据指定类型

       IF 存在Oracle上界结果文件 THEN
           加载Oracle结果作为归一化基准
       IF 存在Lower Bound下界结果文件 THEN
           加载LB结果作为归一化基准

       初始化结果字典 cl_matrix[task_id] = []
   END 初始化

2. 主训练循环:
   FOR each_task t in 任务序列:
       BEGIN 任务处理
           A. 任务准备阶段:
              加载第t个任务配置文件
              IF 指定外部学习率 THEN 覆盖配置中的学习率
              agent.increment_task(task_name, task_config)  // 增量添加新任务

           B. 训练阶段:
              准备训练参数字典 (模型路径, 设备, 数据采样率等)
              IF 训练未完成 AND 非LB模式 THEN
                  调用 task_trainers[trainer_name].main(训练模式)
                  记录训练时间并保存完成标志

           C. 记忆重放阶段:
              IF agent.memory_size > 0 THEN
                  调用 sample_memory() 从当前任务采样存储样本

           D. 评估阶段:
              IF 评估结果不存在 THEN
                  准备评估参数字典
                  调用 evaluate_tasks() 评估所有已见任务
                  保存评估结果到文件系统
              ELSE
                  从已有文件加载评估结果

           E. 任务完成:
              agent.finish_task()  // 清理和状态更新
       END 任务处理

3. 任务评估子流程 (evaluate_tasks):
   输入: agent, 结果字典, 评估参数, 任务列表等

   IF agent是Oracle模式 THEN
       // Oracle只评估当前任务
       BEGIN Oracle评估
           准备当前任务评估目录和参数
           设置模型检查点路径
           IF 结果文件不存在 OR 需要重新评估 THEN
               调用对应trainer进行评估
               保存评估结果
           添加结果到 cl_matrix[current_task]
       END Oracle评估
   ELSE
       // 普通模式评估所有已见任务
       BEGIN 全任务评估
           FOR each_seen_task i from 0 to current_task:
               准备任务i的评估目录和参数
               IF 结果文件不存在 OR 需要重新评估 THEN
                   调用对应trainer评估任务i
                   保存评估结果
               添加结果到 cl_matrix[i]
               IF 存在Oracle结果 THEN
                   计算归一化准确率

           计算遗忘率:
           FOR each_previous_task i from 0 to current_task-1:
               遗忘率 += max(历史最佳性能[i]) - 当前性能[i]
           平均遗忘率 = 总遗忘率 / 已完成任务数
           保存遗忘率到结果字典
       END 全任务评估

4. 结果保存和分析:
   每个任务完成后保存:
   - cl_matrix: 持续学习性能矩阵
   - avg_forgetting: 平均遗忘率
   - final_acc_norm: 最终归一化准确率
   - avg_acc_norm: 平均归一化准确率

=== 关键数据结构 ===
- agent: 持续学习代理，管理模型状态和任务转换
- cl_matrix[i]: 任务i在不同时间点的性能记录
- task_config_dict: 各任务的配置字典
- model_ckpt_list: 模型检查点列表

=== 支持的持续学习策略 ===
- Oracle: 上界基准，每个任务独立训练
- Lower Bound: 下界基准，仅在最后任务上训练
- 记忆重放: 通过sample_memory保留历史样本
- 正则化: 通过mu/beta参数控制遗忘-可塑性平衡
- LoRA微调: 支持低秩适应微调策略
- EMA更新: 指数移动平均模型参数更新

=== 实验输出 ===
最终在output_dir生成:
- 各任务训练模型检查点
- 评估结果文件(YAML格式)
- 持续学习性能分析报告
- 配置文件备份
"""

import os
import sys
import argparse
import numpy as np
import yaml
import random
import agents
import task_trainers
import utils
from pathlib import Path
import time
import datetime

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.backends.cudnn as cudnn
import torch.distributed as dist
from torch.utils.data import DataLoader

# SEED = 0
REVALUATE_FLAG = False


# evaluate on all tasks seen
def evaluate_tasks(
    agent,
    result_dict,
    eval_args,
    task_list,
    oracle_exists,
    oracle_results,
    lb_exists,
    lb_results,
    eval_ema,
):
    # Oracle/LB only evaluates on current task
    if agent.oracle:

        # prepare task files
        task = str(agent.task_id) + "_" + task_list[agent.task_id]["name"]
        out_dir = os.path.join(
            agent.task_dir_dict[task], "_eval-only_" + str(agent.task_id)
        )
        if utils.is_main_process():
            Path(out_dir).mkdir(parents=True, exist_ok=True)
        eval_args["out_dir"] = out_dir
        if eval_args["lb"]:
            eval_args["pretrained"] = agent.model_ckpt_history["pretrained"]
        else:
            eval_args["pretrained"] = agent.model_ckpt_history[task]

        # evaluate the task
        result_file = os.path.join(out_dir, "final_result.yaml")
        will_proceed = True
        if not REVALUATE_FLAG:
            if os.path.exists(result_file):
                try:
                    result = yaml.safe_load(open(result_file, "r"))["result"]
                    if result > 0:
                        will_proceed = False
                    else:
                        will_proceed = True
                except:
                    will_proceed = True
        if will_proceed:
            result = task_trainers.__dict__[task_list[agent.task_id]["trainer"]].main(
                args=eval_args,
                config=agent.task_config_dict[task],
                eval=True,
                test_ema=eval_ema,
            )
            if utils.is_main_process():
                try:
                    result = float(result)
                    result = result.item()
                except:
                    pass
                yaml.dump(
                    {"result": result}, open(result_file, "w"), default_flow_style=False
                )
        if utils.is_main_process():
            result_dict["cl_matrix"][agent.task_id].append(result)
    # evaluate on all seen tasks
    else:
        acc_norm = []
        for t in range(agent.task_id + 1):
            # prepare task files
            task = str(t) + "_" + task_list[t]["name"]
            out_dir = os.path.join(
                agent.task_dir_dict[task], "_eval-only_" + str(agent.task_id)
            )
            if utils.is_main_process():
                Path(out_dir).mkdir(parents=True, exist_ok=True)
            eval_args["out_dir"] = out_dir
            eval_args["pretrained"] = agent.model_ckpt_list

            # evaluate the task
            result_file = os.path.join(out_dir, "final_result.yaml")
            will_proceed = True
            if not REVALUATE_FLAG:
                if os.path.exists(result_file):
                    try:
                        result = yaml.safe_load(open(result_file, "r"))["result"]
                        if result > 0:
                            will_proceed = False
                        else:
                            will_proceed = True
                    except:
                        will_proceed = True
            if will_proceed:

                result = task_trainers.__dict__[task_list[t]["trainer"]].main(
                    args=eval_args,
                    config=agent.task_config_dict[task],
                    eval=True,
                    test_ema=eval_ema,
                )

                # process the task results
                if utils.is_main_process():
                    try:
                        result = float(result)
                        result = result.item()
                    except:
                        pass
                    yaml.dump(
                        {"result": result},
                        open(result_file, "w"),
                        default_flow_style=False,
                    )

            if utils.is_main_process():
                result_dict["cl_matrix"][t].append(result)
                if oracle_exists:
                    if lb_exists:
                        acc_norm.append(
                            (result - lb_results[t][0])
                            / (oracle_results[t][0] - lb_results[t][0])
                        )
                    else:
                        acc_norm.append(result / oracle_results[t][0])

        # post process task eval
        if utils.is_main_process():
            # compute step (final) forgetting rate
            forg_i = 0.0
            for i in range(agent.task_id):
                to_add = (
                    max(result_dict["cl_matrix"][i]) - result_dict["cl_matrix"][i][-1]
                )
                forg_i += to_add
            if agent.task_id == 0:
                forg_cur = 0.0
            else:
                forg_cur = forg_i / (agent.task_id)
            result_dict["avg_forgetting"][agent.task_id] = forg_cur

    return result_dict


# train on task sequence
def trainer(args, configs, zs_config):
    # fix the seed for reproducibility

    torch.backends.cudnn.deterministic = True
    SEED = args.seed
    # seed = SEED + utils.get_rank()
    seed = SEED + utils.get_rank()
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    cudnn.benchmark = True

    # init world
    utils.init_distributed_mode(args)
    device = torch.device(args.device)

    # create agent
    agent_config = {
        "output_dir": args.output_dir,
        "pretrained": configs["pretrained"],
        "oracle": args.oracle_flag or args.lb_flag,
        "mu": args.mu,
        "beta": args.beta,
        "text_only_flag": args.text_only_flag,
        "vision_only_flag": args.vision_only_flag,
        "global_args": args,
        "type": args.ema,
    }
    agent = agents.__dict__[args.agent_type].__dict__[args.agent_name](agent_config)

    # get tasks
    task_list = configs["task_list"]

    zsl_task_list = zs_config["task_list"]

    n_tasks = len(task_list)

    # n_tasks = 7  # 注释掉硬编码，使用实际任务列表长度
    # do we have an oracle? used to normalize results for averaging
    oracle_file = os.path.join(
        os.path.dirname(args.output_dir), "UB/final_results/cl_matrix.yaml"
    )
    if not os.path.exists(oracle_file):
        oracle_file = os.path.join(
            os.path.dirname(os.path.dirname(args.output_dir)),
            "UB/final_results/cl_matrix.yaml",
        )
    if not agent.oracle and os.path.exists(oracle_file):
        oracle_exists = True
        oracle_results = yaml.safe_load(open(oracle_file, "r"))
    else:
        oracle_exists = False
        oracle_results = None

    # do we have a LB? used to normalize results for averaging
    lb_file = os.path.join(
        os.path.dirname(args.output_dir), "LB/final_results/cl_matrix.yaml"
    )
    if not os.path.exists(lb_file):
        lb_file = os.path.join(
            os.path.dirname(os.path.dirname(args.output_dir)),
            "LB/final_results/cl_matrix.yaml",
        )
    if not agent.oracle and os.path.exists(lb_file):
        lb_exists = True
        lb_results = yaml.safe_load(open(lb_file, "r"))
    else:
        lb_exists = False
        lb_results = None

    # create results dictionary
    result_dict = {}
    if agent.oracle:
        result_keys = ["cl_matrix"]
    elif oracle_exists:
        result_keys = ["cl_matrix", "final_acc_norm", "avg_acc_norm", "avg_forgetting"]
    else:
        result_keys = ["cl_matrix", "avg_forgetting"]
    result_dict["cl_matrix"] = [[] for t in range(n_tasks)]
    result_dict["final_acc_norm"] = [-1 for t in range(n_tasks)]
    result_dict["avg_acc_norm"] = [-1 for t in range(n_tasks)]
    result_dict["avg_forgetting"] = [-1 for t in range(n_tasks)]

    # increment over tasks

    for t in range(len(task_list)):

        # get task
        task = str(t) + "_" + task_list[t]["name"]
        task_config = yaml.load(open(task_list[t]["config"], "r"), Loader=yaml.Loader)

        if args.external_lr >= 0:
            print("Overriding external LR")
            task_config["init_lr"] = args.external_lr

        with open(
            os.path.join(args.output_dir, "config_task-" + task + ".yaml"), "w"
        ) as tcf:
            yaml.dump(task_config, tcf)
        if args.debug_flag:
            task_config["max_epoch"] = 1
        agent.increment_task(task, task_config)

        # create task args dict
        task_args = {
            "out_dir": agent.task_dir_dict[task],
            "model_load_path": agent.model_ckpt_list,
            "model_save_path": agent.model_ckpt_history[agent.tasks[-1]],
            "device": device,
            "training_data_sample": args.training_data_sample,
            "distributed": args.distributed,
            "gpu": args.gpu,
            "pretrained": agent.model_ckpt_load,
            "agent": agent,
            "num_workers": args.num_workers,
            "eval_every": args.eval_every,
            "train_eval_type": args.train_eval_type,
            "flush_queue": args.flush_queue,
            "ema": args.ema,
            "ema_alpha": args.ema_alpha,
            "ema_lora": args.ema_lora,
            "ema_frequency": args.epoch_frequency,
            "save_frequency": args.save_frequency,
            "model": args.model,
            "size": args.size,
        }

        # train task
        training_complete_file = os.path.join(
            agent.task_dir_dict[task], "training_complete.log"
        )
        cur_task_config = agent.task_config_dict[task]
        cur_task_config["task_seq_name"] = task_list[t]["name"]
        cur_task_config["json_files"] = task_list[t].get("json_files", None)
        cur_task_config["zsl_json_files"] = zsl_task_list[0].get("json_files", None)

        cur_task_config["task_id_for_debug"] = t
        if not os.path.exists(training_complete_file) and not args.lb_flag:
            if utils.is_main_process():
                print("Start training task + " + str())
            start_time = time.time()
            task_trainers.__dict__[task_list[t]["trainer"]].main(
                args=task_args, config=cur_task_config, eval=False, test_ema=False
            )
            total_time = time.time() - start_time
            total_time_str = str(datetime.timedelta(seconds=int(total_time)))
            if utils.is_main_process():
                print("Training time {}".format(total_time_str))
            with open(training_complete_file, "w") as f:
                f.write(total_time_str)

        # rehearsal
        if agent.memory_size > 0:
            # agent.coreset.extend(
            task_trainers.__dict__[task_list[t]["trainer"]].sample_memory(
                memory_size=agent.memory_size, args=task_args, config=cur_task_config
            )
            # )

        # evaluate
        if not os.path.isdir(os.path.join(args.result_dir, f"task_{t:02d}")):
            eval_args = {
                "device": device,
                "training_data_sample": args.training_data_sample,
                "distributed": args.distributed,
                "gpu": args.gpu,
                "agent": agent,
                "lb": args.lb_flag,
                "num_workers": args.num_workers,
                "fast_eval": args.fast_eval,
                "flush_queue": args.flush_queue,
                "ema": args.ema,
                "ema_alpha": args.ema_alpha,
                "ema_lora": args.ema_lora,
                "ema_frequency": args.epoch_frequency,
                "save_frequency": args.save_frequency,
                "model": args.model,
                "size": args.size,
            }

            result_dict = evaluate_tasks(
                agent,
                result_dict,
                eval_args,
                task_list,
                oracle_exists,
                oracle_results,
                lb_exists,
                lb_results,
                agent.ema,
            )

            # save results
            if utils.is_main_process():
                save_dir = args.result_dir
                for rkey in result_keys:
                    with open(os.path.join(save_dir, rkey + ".yaml"), "w") as yaml_file:
                        yaml.dump(
                            result_dict[rkey], yaml_file, default_flow_style=False
                        )
                save_dir = os.path.join(args.result_dir, f"task_{t:02d}")
                os.makedirs(save_dir, exist_ok=True)
                for rkey in result_keys:
                    with open(os.path.join(save_dir, rkey + ".yaml"), "w") as yaml_file:
                        yaml.dump(
                            result_dict[rkey], yaml_file, default_flow_style=False
                        )
        else:
            prev_task_dir = os.path.join(args.result_dir, f"task_{t:02d}")
            for rkey in result_keys:
                with open(os.path.join(prev_task_dir, rkey + ".yaml"), "r") as f:
                    result_dict[rkey] = yaml.safe_load(f)

        # finish task
        agent.finish_task()


def get_args():
    parser = argparse.ArgumentParser()

    # benchmark
    parser.add_argument("--config", default="./configs/nvlr_task_order/base.yaml")
    parser.add_argument("--output_dir", default="output/continual")
    parser.add_argument(
        "--repeat", type=int, default=1, help="Repeat the experiment N times"
    )
    parser.add_argument(
        "--overwrite",
        type=int,
        default=0,
        metavar="N",
        help="Train regardless of whether saved model exists",
    )
    parser.add_argument("--device", default="cuda")
    parser.add_argument(
        "--eval_every", type=int, default=1, help="Reduce validation data evals"
    )

    # distributed training
    parser.add_argument(
        "--world_size", default=1, type=int, help="number of distributed processes"
    )
    parser.add_argument(
        "--dist_url", default="env://", help="url used to set up distributed training"
    )
    parser.add_argument("--distributed", default=True, type=bool)
    parser.add_argument(
        "--local_rank",
        default=os.environ.get("LOCAL_RANK", 0),
        type=int,
        help="Please ignore and do not set this argument.",
    )
    parser.add_argument("--debug", action="store_true", help="do debug")
    parser.add_argument("--debug_port", default=12345, type=int, help="for debug")
    parser.add_argument("--num_workers", default=2, type=int, help="for debug")
    parser.add_argument("--debug_addr", type=str, help="for debug")
    parser.add_argument(
        "--training_data_sample",
        default=1.0,
        type=float,
        help="% training data to use.",
    )
    parser.add_argument("--memory", default=0, type=int, help="coreset to retain")

    # continual learning
    parser.add_argument(
        "--agent_type", type=str, default="base", help="Base file of continual learner"
    )
    parser.add_argument(
        "--agent_name",
        type=str,
        default="Naive",
        help="Class name of continual learner",
    )
    parser.add_argument(
        "--oracle_flag",
        default=False,
        action="store_true",
        help="Upper bound for oracle",
    )
    parser.add_argument(
        "--lb_flag", default=False, action="store_true", help="Lower bound"
    )
    parser.add_argument(
        "--debug_flag",
        default=False,
        action="store_true",
        help="Debug mode to run faster",
    )
    parser.add_argument("--mu", type=float, default=1.0, help="regularization strength")
    parser.add_argument(
        "--external_lr", type=float, default=-1.0, help="regularization strength"
    )
    parser.add_argument(
        "--beta", type=float, default=0.0, help="regularization strength"
    )
    parser.add_argument(
        "--text_only_flag",
        default=False,
        action="store_true",
        help="only regulalarize text models",
    )
    parser.add_argument(
        "--vision_only_flag",
        default=False,
        action="store_true",
        help="only regularize vision models",
    )
    parser.add_argument(
        "--fast_eval",
        default=False,
        action="store_true",
        help="applies fast eval for multi-lora",
    )
    parser.add_argument(
        "--train_eval_type", type=str, default="slow", help="for multi-lora training"
    )  # slow / fast / last
    parser.add_argument(
        "--loss_alpha", type=float, default=1.0, help="for extra losses"
    )
    parser.add_argument(
        "--auto_scale_alpha",
        default=False,
        action="store_true",
        help="for auto-scaling extra losses",
    )
    parser.add_argument(
        "--skip_base_keep",
        default=False,
        action="store_true",
        help="for not keeping model -1 in adv V2",
    )
    parser.add_argument(
        "--force_keep", type=int, default=None, help="for adv samples CL"
    )
    parser.add_argument(
        "--num_adv_iters", type=int, default=11, help="for adv samples CL"
    )
    parser.add_argument(
        "--adv_step_sz", type=float, default=0.1, help="for adv samples CL"
    )

    # ablations
    parser.add_argument(
        "--adv_last_only", default=False, action="store_true", help="for adv samples CL"
    )
    parser.add_argument(
        "--adv_num_last", type=int, default=1, help="for adv samples CL"
    )
    parser.add_argument(
        "--adv_pos", default=False, action="store_true", help="for adv samples CL"
    )

    # other
    parser.add_argument(
        "--freeze_text_emb", default=False, action="store_true", help="for lora"
    )
    parser.add_argument(
        "--flush_queue",
        default=False,
        action="store_true",
        help="empty the queue before each task",
    )

    # EMA setting
    parser.add_argument(
        "--ema", type=str, default="task", help="for ema updating"
    )  # task/epoch/mix
    parser.add_argument(
        "--ema_alpha", type=float, default=0.999, help="for epoch ema updating"
    )
    parser.add_argument(
        "--epoch_frequency", type=int, default=1, help="for epoch ema update frequency"
    )
    parser.add_argument(
        "--save_frequency", type=str, default="every", help="for epoch ema save"
    )  # every/best

    parser.add_argument(
        "--ema_lora", type=str, default="continual", help="for lora initial"
    )  # continual/zero/ema

    # zsl config
    parser.add_argument(
        "--zsl_config", default="./configs/nvlr_task_order/zero_shot.yaml"
    )

    # model config
    parser.add_argument(
        "--model", type=str, default="blip", help="for model type choosing"
    )
    parser.add_argument("--size", type=str, default="base", help="for beit model size")

    parser.add_argument("--seed", type=int, default=0)
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    # debug
    if args.debug:
        set_remote_debugger(debug_port=args.debug_port, debug_ip=args.debug_addr)

    args.output_dir = args.output_dir.format(**args.__dict__)
    print(f"Output dir: {args.output_dir}")

    # configs, output directories, and such
    args.result_dir = os.path.join(args.output_dir, "final_results")
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    Path(args.result_dir).mkdir(parents=True, exist_ok=True)
    configs = yaml.load(open(args.config, "r"), Loader=yaml.Loader)

    zs_config = yaml.load(open(args.zsl_config, "r"), Loader=yaml.Loader)

    yaml.dump(configs, open(os.path.join(args.output_dir, "config_sequence.yaml"), "w"))
    yaml.dump(args, open(os.path.join(args.output_dir, "args.yaml"), "w"))

    # let's gooooooo
    trainer(args, configs, zs_config)
