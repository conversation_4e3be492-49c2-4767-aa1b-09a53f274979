
#export CUDA_VISIBLE_DEVICES=2,3,4,5,6,7
#torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/2task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/2zero_shot_gqa.yaml

#torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/3task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/3zero_shot_gqa.yaml
#

#torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/4task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/4zero_shot_gqa.yaml
#

#torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/5task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/5zero_shot_gqa.yaml
#
torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/6task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/6zero_shot_gqa.yaml
#
torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/7task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/7zero_shot_gqa.yaml



torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/2task_gqa_base+cap.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/base+cap_7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/2zero_shot_gqa.yaml

torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/3task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/base+cap_7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/3zero_shot_gqa.yaml
#

torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/4task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/base+cap_7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/4zero_shot_gqa.yaml
#

torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/5task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/base+cap_7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/5zero_shot_gqa.yaml
#
torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/6task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/base+cap_7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/6zero_shot_gqa.yaml
#
torchrun --nproc_per_node=8 --master_port=25334 run_me.py --config ./configs/gqa_new_wild/7task_gqa.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --ema epoch --ema_lora  ema --ema_alpha 0.85  --save_frequency  every --output_dir /root/xingxing/project/result_gqa/base+cap_7task_MultiLoRa_wild_LLM_id/  --zsl_config ./configs/gqa_new_wild/7zero_shot_gqa.yaml
