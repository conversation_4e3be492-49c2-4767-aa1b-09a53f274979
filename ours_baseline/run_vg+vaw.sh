torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/2task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id/  --zsl_config ./configs/final_vg+vaw/2zero_shot.yaml
#
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/3task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id/  --zsl_config ./configs/final_vg+vaw/3zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/4task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id/  --zsl_config ./configs/final_vg+vaw/4zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/5task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id/  --zsl_config ./configs/final_vg+vaw/5zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/6task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id/  --zsl_config ./configs/final_vg+vaw/6zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/7task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id/  --zsl_config ./configs/final_vg+vaw/7zero_shot.yaml





torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/2task_VG+VAW_checklist_base+cap.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_cap/  --zsl_config ./configs/final_vg+vaw/2zero_shot.yaml
#
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/3task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_cap/  --zsl_config ./configs/final_vg+vaw/3zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/4task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_cap/  --zsl_config ./configs/final_vg+vaw/4zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/5task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_cap/  --zsl_config ./configs/final_vg+vaw/5zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/6task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_cap/  --zsl_config ./configs/final_vg+vaw/6zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/7task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_cap/  --zsl_config ./configs/final_vg+vaw/7zero_shot.yaml



torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/2task_VG+VAW_checklist_nvlr.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_nvlr/  --zsl_config ./configs/final_vg+vaw/2zero_shot.yaml
#
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/3task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_nvlr/  --zsl_config ./configs/final_vg+vaw/3zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/4task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_nvlr/  --zsl_config ./configs/final_vg+vaw/4zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/5task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_nvlr/  --zsl_config ./configs/final_vg+vaw/5zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/6task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_nvlr/  --zsl_config ./configs/final_vg+vaw/6zero_shot.yaml
##
torchrun --nproc_per_node=8  --master_port=25334 run_me.py --config ./configs/final_vg+vaw/7task_VG+VAW_checklist.yaml --eval_every 1 --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125  --save_frequency  every --output_dir /root/xingxing/project/result_nvlr/vg+vaw_wild_id_nvlr/  --zsl_config ./configs/final_vg+vaw/7zero_shot.yaml