torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/2task_VG_checklist.yaml --zsl_config ./configs/final_vg/2zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/3task_VG_checklist.yaml --zsl_config ./configs/final_vg/3zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/4task_VG_checklist.yaml --zsl_config ./configs/final_vg/4zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/5task_VG_checklist.yaml --zsl_config ./configs/final_vg/5zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/6task_VG_checklist.yaml --zsl_config ./configs/final_vg/6zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/7task_VG_checklist.yaml --zsl_config ./configs/final_vg/7zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base





torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/2task_VG_checklist_base+cap.yaml --zsl_config ./configs/final_vg/2zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/3task_VG_checklist.yaml --zsl_config ./configs/final_vg/3zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/4task_VG_checklist.yaml --zsl_config ./configs/final_vg/4zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/5task_VG_checklist.yaml --zsl_config ./configs/final_vg/5zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/6task_VG_checklist.yaml --zsl_config ./configs/final_vg/6zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/7task_VG_checklist.yaml --zsl_config ./configs/final_vg/7zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_base+cap




torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/2task_VG_checklist_nvlr.yaml --zsl_config ./configs/final_vg/2zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/3task_VG_checklist.yaml --zsl_config ./configs/final_vg/3zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/4task_VG_checklist.yaml --zsl_config ./configs/final_vg/4zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/5task_VG_checklist.yaml --zsl_config ./configs/final_vg/5zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/6task_VG_checklist.yaml --zsl_config ./configs/final_vg/6zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vg/7task_VG_checklist.yaml --zsl_config ./configs/final_vg/7zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vg_wild_id_nvlr



torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/2task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/2zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/3task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/3zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/4task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/4zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/5task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/5zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base





torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/2task_VAW_checklist_base+cap.yaml --zsl_config ./configs/final_vaw/2zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/3task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/3zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/4task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/4zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base+cap

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/5task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/5zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_base+cap





torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/2task_VAW_checklist_nvlr.yaml --zsl_config ./configs/final_vaw/2zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/3task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/3zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/4task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/4zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_nvlr

torchrun --nproc_per_node=8 --master_port=25334 run_me.py   --config ./configs/final_vaw/5task_VAW_checklist.yaml --zsl_config ./configs/final_vaw/5zero_shot.yaml --eval_every 1  --freeze_text_emb --agent_type lora --agent_name MultiLoRa_wild --mu 16 --external_lr 0.00125 --save_frequency every --output_dir /root/xingxing/project/result_nvlr/vaw_wild_id_nvlr
