import os
import json
import uuid


def convert_data(source_dir, target_dir):
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)

    for scene_dir in os.listdir(source_dir):
        scene_path = os.path.join(source_dir, scene_dir)
        if not os.path.isdir(scene_path):
            continue

        qa_file_path = os.path.join(scene_path, "qa.jsonl")
        if not os.path.exists(qa_file_path):
            continue

        output_data = []
        with open(qa_file_path, "r") as f:
            for line in f:
                data = json.loads(line)
                image_path = os.path.join(scene_dir, data["vp_img"])

                for q in data["questions"]:
                    output_data.append(
                        {
                            "question_id": str(uuid.uuid4()),
                            "question": q["question"],
                            "answer": [q["answer"]],
                            "image": [image_path],
                            "dataset": "eqa",
                        }
                    )

        output_file_path = os.path.join(target_dir, f"{scene_dir}.json")
        with open(output_file_path, "w") as f:
            json.dump(output_data, f, indent=4)


if __name__ == "__main__":
    source_directory = "data/finetune_np/HM3D"
    target_directory = "datasets/eqa"
    convert_data(source_directory, target_directory)
    print(f"Data conversion complete. Files saved in {target_directory}")
