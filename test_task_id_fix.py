#!/usr/bin/env python3
"""
测试脚本：验证task_id修复是否有效
"""

import os
import sys
import yaml
from pathlib import Path

# 添加项目路径
sys.path.append('ours_baseline')

# 导入必要的模块
import agents
import utils

def test_task_id_logic():
    """测试task_id的逻辑是否正确"""
    
    print("=== 测试 task_id 逻辑 ===")
    
    # 创建一个简单的agent配置
    agent_config = {
        'pretrained': None,
        'oracle': False,
        'mu': 0.0,
        'beta': 0.0,
        'text_only_flag': False,
        'vision_only_flag': False,
        'global_args': None,
        'type': False,
        'output_dir': '/tmp/test_agent'
    }
    
    # 创建agent
    agent = agents.base.Base(agent_config)
    
    print(f"初始 task_id: {agent.task_id}")
    print(f"初始 tasks: {agent.tasks}")
    
    # 模拟添加第一个任务
    task1_config = {'max_epoch': 1}
    agent.increment_task("0_task1", task1_config)
    
    print(f"添加第一个任务后 task_id: {agent.task_id}")
    print(f"添加第一个任务后 tasks: {agent.tasks}")
    
    # 模拟添加第二个任务
    task2_config = {'max_epoch': 1}
    agent.increment_task("1_task2", task2_config)
    
    print(f"添加第二个任务后 task_id: {agent.task_id}")
    print(f"添加第二个任务后 tasks: {agent.tasks}")
    
    # 测试训练逻辑判断
    print("\n=== 测试训练逻辑判断 ===")
    
    # 模拟第一个任务的训练判断
    agent.task_id = 0  # 重置为第一个任务
    agent.zaf = True
    condition1 = agent.task_id != 0 and agent.zaf == True
    print(f"第一个任务 (task_id=0): condition (task_id != 0 and zaf) = {condition1}")
    print(f"应该使用: {'ZSL训练' if condition1 else '常规训练'}")
    
    # 模拟第二个任务的训练判断
    agent.task_id = 1  # 第二个任务
    condition2 = agent.task_id != 0 and agent.zaf == True
    print(f"第二个任务 (task_id=1): condition (task_id != 0 and zaf) = {condition2}")
    print(f"应该使用: {'ZSL训练' if condition2 else '常规训练'}")
    
    print("\n=== 测试完成 ===")

def test_training_complete_file_logic():
    """测试训练完成文件的逻辑"""
    
    print("\n=== 测试训练完成文件逻辑 ===")
    
    # 创建测试目录
    test_dir = "/tmp/test_training_complete"
    Path(test_dir).mkdir(parents=True, exist_ok=True)
    
    # 测试文件不存在的情况
    training_complete_file = os.path.join(test_dir, "training_complete.log")
    exists = os.path.exists(training_complete_file)
    lb_flag = False
    
    will_train = not exists and not lb_flag
    print(f"训练完成文件存在: {exists}")
    print(f"LB标志: {lb_flag}")
    print(f"将会训练: {will_train}")
    
    # 创建训练完成文件
    with open(training_complete_file, "w") as f:
        f.write("Training completed in 00:01:00")
    
    # 再次测试
    exists = os.path.exists(training_complete_file)
    will_train = not exists and not lb_flag
    print(f"创建文件后 - 训练完成文件存在: {exists}")
    print(f"创建文件后 - 将会训练: {will_train}")
    
    # 清理
    os.remove(training_complete_file)
    os.rmdir(test_dir)
    
    print("=== 训练完成文件测试完成 ===")

if __name__ == "__main__":
    test_task_id_logic()
    test_training_complete_file_logic()
